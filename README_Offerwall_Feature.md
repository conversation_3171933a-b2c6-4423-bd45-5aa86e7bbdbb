# 🎯 Offerwall - Documentación Técnica

## 📝 Resumen

El módulo de Offerwall permite a los usuarios de Piñata Party ganar SweetGems completando ofertas de terceros. Esta funcionalidad está integrada con múltiples proveedores de ofertas y ofrece un sistema seguro de acreditación de recompensas.

## 🌐 Proveedores Integrados

### 🎮 AdGem
- **Tipo**: Iframe offerwall
- **Cobertura**: Global (óptimo para LATAM)
- **Recompensas**: Acreditación automática de SweetGems
- **Documentación**: [AdGem Help Center](https://www.adgem.com/help/)

**Configuración Técnica**:
```
App ID: 30519
Postback URL: https://us-central1-portafoliodev-bd7f3.cloudfunctions.net/offerwallCallbackAdgem
Postback Key: hhha9f9g64h807ackda78f65
```

### 🏆 Wannads
- **Tipo**: Iframe offerwall + surveywall
- **API Key**: 68377ea67da48926033093
- **URLs**:
  - Offerwall: `https://earn.wannads.com/wall?apiKey=[API_KEY]&userId=[USER_ID]`
  - Surveywall: `https://earn.wannads.com/surveywall?apiKey=[API_KEY]&userId=[USER_ID]`
- **Postback URL**: `https://us-central1-portafoliodev-bd7f3.cloudfunctions.net/wannadsPostback`

## 🗃️ Estructura de Datos

### Colección: `offerCompletions`
```javascript
{
  userId: string,           // ID del usuario
  provider: string,         // 'AdGem', 'Wannads'
  offerId: string,         // ID de la oferta
  amount: number,          // Cantidad de la oferta
  payout: number,          // Valor en USD
  transactionId: string,   // ID único de transacción
  status: string,          // 'completed'|'pending'|'rejected'
  ipAddress: string,       // IP del usuario
  country: string,         // Código de país
  timestamp: timestamp,    // Fecha de la transacción
  rawData: object          // Datos originales
}
```

### Documento de Usuario
```javascript
{
  // ... otros campos ...
  offerwallEarnings: number,    // Total acumulado
  lastOfferwallEarning: number, // Última ganancia
  lastOfferwallDate: timestamp, // Fecha última oferta
  // ...
}
```

## 🔄 Flujo de Trabajo

1. **Acreditación de Recompensas**
   - El usuario completa una oferta en el iframe
   - El proveedor envía un callback a nuestro servidor
   - Validamos la transacción
   - Actualizamos el saldo del usuario
   - Registramos la transacción

2. **Verificación de Seguridad**
   - Validación de firmas
   - Prevención de duplicados
   - Registro de auditoría

3. **Monitoreo**
   - Dashboard de transacciones
   - Logs de errores
   - Métricas de rendimiento

## 🛠️ Implementación Técnica

### Estructura del Proyecto
```
lib/
├── models/
│   └── user_data.dart           # Modelo de usuario
├── screens/
│   └── user/
│       ├── offerwall_screen.dart  # Pantalla de ofertas
│       └── dashboard_screen.dart  # Dashboard de usuario
└── services/
    └── offerwall_service.dart  # Lógica de negocio

firebase/
└── functions/
    ├── index.js              # Cloud Functions
    └── src/
        ├── adgem_callback.js  # Callback de AdGem
        └── wannads_callback.js # Callback de Wannads
```

### Seguridad
- Validación de firmas en callbacks
- Protección contra transacciones duplicadas
- Registro detallado de auditoría
- Límites de frecuencia

## 🚀 Próximas Mejoras

### En Desarrollo
- [ ] Notificaciones push para recompensas
- [ ] Panel de estadísticas
- [ ] Sistema de logros
- [ ] Más proveedores de ofertas

### Métricas Clave
- Tasa de conversión
- Valor promedio por oferta
- Retención de usuarios

## ❓ Soporte

Para asistencia técnica:
- **Email**: <EMAIL>
- **Slack**: #offerwall-dev
- **Documentación**: [Confluence](https://pinataparty.atlassian.net/wiki/offerwall)

## Próximos Pasos

1. **Investigación Inmediata**:
   - Registrarse en OfferToro y AdGate Media
   - Revisar documentación técnica detallada
   - Solicitar acceso a APIs de testing

2. **Prototipo Rápido**:
   - Crear integración básica con iframe de OfferToro
   - Implementar modelo de datos mínimo
   - Probar flujo completo end-to-end

3. **Validación**:
   - Testing con usuarios beta
   - Medición de métricas iniciales
   - Iteración basada en feedback

## Recursos y Referencias

- [OfferToro Publisher Documentation](https://offertoro.com/publishers/)
- [AdGate Media Integration Guide](https://adgatemedia.com/integration)
- [Flutter Web Iframe Integration](https://flutter.dev/docs/development/platform-integration/web)
- [Firebase Webhooks Best Practices](https://firebase.google.com/docs/functions/http-events)

## Modelos de Datos Detallados

### 1. Offer (Oferta Individual)
```dart
class Offer {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final double rewardAmount;
  final String currency;
  final String category; // 'apps', 'surveys', 'signup', 'shopping'
  final String provider; // 'OfferToro', 'AdGate', etc.
  final String targetUrl;
  final List<String> requirements;
  final int estimatedTimeMinutes;
  final DateTime expirationDate;
  final bool isActive;
  final Map<String, dynamic> metadata;
  final List<String> supportedCountries;
  final String difficulty; // 'easy', 'medium', 'hard'
}
```

### 2. OfferCompletion (Registro de Completación)
```dart
class OfferCompletion {
  final String id;
  final String userId;
  final String offerId;
  final String provider;
  final DateTime startedAt;
  final DateTime? completedAt;
  final DateTime? verifiedAt;
  final String status; // 'started', 'completed', 'verified', 'rejected', 'pending'
  final double rewardAmount;
  final String currency;
  final String? rejectionReason;
  final Map<String, dynamic> providerData;
  final String? transactionId; // ID del proveedor
  final int retryCount;
  final DateTime? lastRetryAt;
}
```

### 3. OfferwallConfig (Configuración de Plataformas)
```dart
class OfferwallConfig {
  final String provider;
  final String apiKey;
  final String secretKey;
  final String baseUrl;
  final String callbackUrl;
  final bool isActive;
  final Map<String, String> countrySettings;
  final double conversionRate; // Puntos a USD
  final int minimumPayout;
  final List<String> supportedCategories;
  final Map<String, dynamic> customSettings;
}
```

### 4. OfferwallEarnings (Ganancias de Offerwall)
```dart
class OfferwallEarnings {
  final String userId;
  final double totalEarnings;
  final double availableEarnings;
  final double pendingEarnings;
  final int completedOffers;
  final int pendingOffers;
  final int rejectedOffers;
  final DateTime lastOfferDate;
  final Map<String, double> earningsByProvider;
  final Map<String, int> offersByCategory;
  final double averageRewardPerOffer;
}
```

## Estructura de Firebase Extendida

### Colecciones Nuevas

```
projects/
  - pinataparty/
    - offerwall/
      - config/
        - providers/
          - {providerId}/
            - name: string
            - apiKey: string
            - secretKey: string
            - baseUrl: string
            - callbackUrl: string
            - isActive: boolean
            - conversionRate: number
            - minimumPayout: number
            - countrySettings: object
            - supportedCategories: array
            - customSettings: object
            - createdAt: timestamp
            - updatedAt: timestamp

        - global/
          - isOfferwallEnabled: boolean
          - defaultProvider: string
          - maxOffersPerUser: number
          - cooldownHours: number
          - fraudDetectionEnabled: boolean

      - offers/
        - {offerId}/
          - title: string
          - description: string
          - imageUrl: string
          - rewardAmount: number
          - currency: string
          - category: string
          - provider: string
          - targetUrl: string
          - requirements: array
          - estimatedTimeMinutes: number
          - expirationDate: timestamp
          - isActive: boolean
          - metadata: object
          - supportedCountries: array
          - difficulty: string
          - createdAt: timestamp
          - updatedAt: timestamp

      - completions/
        - {completionId}/
          - userId: string
          - offerId: string
          - provider: string
          - startedAt: timestamp
          - completedAt: timestamp (opcional)
          - verifiedAt: timestamp (opcional)
          - status: string
          - rewardAmount: number
          - currency: string
          - rejectionReason: string (opcional)
          - providerData: object
          - transactionId: string (opcional)
          - retryCount: number
          - lastRetryAt: timestamp (opcional)

      - earnings/
        - {userId}/
          - totalEarnings: number
          - availableEarnings: number
          - pendingEarnings: number
          - completedOffers: number
          - pendingOffers: number
          - rejectedOffers: number
          - lastOfferDate: timestamp
          - earningsByProvider: object
          - offersByCategory: object
          - averageRewardPerOffer: number
          - lastUpdated: timestamp

      - analytics/
        - daily/
          - {date}/
            - totalOffers: number
            - completedOffers: number
            - totalEarnings: number
            - uniqueUsers: number
            - byProvider: object
            - byCategory: object

        - user_stats/
          - {userId}/
            - totalOffers: number
            - completionRate: number
            - averageTimeToComplete: number
            - favoriteCategory: string
            - lastActiveDate: timestamp
```

## Servicios Principales Detallados

### 1. OfferwallService (Servicio Principal)
```dart
class OfferwallService {
  // Gestión de ofertas
  Future<List<Offer>> getAvailableOffers(String userId, {
    String? category,
    String? provider,
    int limit = 20,
    String? country,
  });

  Future<Offer?> getOfferById(String offerId);
  Future<List<Offer>> getOffersByCategory(String category);
  Future<void> syncOffersFromProviders();

  // Gestión de completaciones
  Future<String> startOffer(String userId, String offerId);
  Future<void> completeOffer(String completionId, Map<String, dynamic> providerData);
  Future<List<OfferCompletion>> getUserCompletions(String userId, {
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  });

  Future<void> verifyCompletion(String completionId);
  Future<void> retryVerification(String completionId);

  // Gestión de ganancias
  Future<OfferwallEarnings> getUserEarnings(String userId);
  Future<void> updateEarnings(String userId, double amount, String provider);
  Future<void> processEarningsDistribution();

  // Webhooks y callbacks
  Future<void> handleProviderCallback(String provider, Map<String, dynamic> data);
  Future<bool> validateCallback(String provider, Map<String, dynamic> data, String signature);

  // Analytics
  Future<Map<String, dynamic>> getOfferwallAnalytics(DateTime startDate, DateTime endDate);
  Future<void> updateUserStats(String userId);
}
```

### 2. OfferToroService (Implementación Específica)
```dart
class OfferToroService extends OfferwallProviderService {
  static const String baseUrl = 'https://www.offertoro.com/api/v1/';

  @override
  Future<List<Offer>> fetchOffers(String userId, String country) async {
    final response = await http.get(
      Uri.parse('${baseUrl}offers'),
      headers: {
        'Authorization': 'Bearer $apiKey',
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['offers'].map<Offer>((offer) => Offer.fromOfferToro(offer)).toList();
    }
    throw Exception('Failed to fetch offers from OfferToro');
  }

  @override
  Future<String> generateOfferUrl(String userId, String offerId) async {
    return '${baseUrl}click?user_id=$userId&offer_id=$offerId&pub_id=$publisherId';
  }

  @override
  Future<bool> verifyCompletion(String completionId) async {
    // Implementar verificación específica de OfferToro
    return true;
  }

  @override
  Future<void> handleCallback(Map<String, dynamic> callbackData) async {
    // Procesar callback específico de OfferToro
    final userId = callbackData['user_id'];
    final offerId = callbackData['offer_id'];
    final reward = double.parse(callbackData['reward'].toString());

    await _processCompletion(userId, offerId, reward, callbackData);
  }
}
```

### 3. AdGateService (Implementación Específica)
```dart
class AdGateService extends OfferwallProviderService {
  static const String baseUrl = 'https://api.adgatemedia.com/v3/';

  @override
  Future<List<Offer>> fetchOffers(String userId, String country) async {
    final response = await http.get(
      Uri.parse('${baseUrl}offers'),
      headers: {
        'Authorization': 'Bearer $apiKey',
        'User-Agent': 'PinataParty/1.0',
      },
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['data'].map<Offer>((offer) => Offer.fromAdGate(offer)).toList();
    }
    throw Exception('Failed to fetch offers from AdGate Media');
  }

  @override
  Future<String> generateOfferUrl(String userId, String offerId) async {
    return '${baseUrl}click?wall=$wallId&user_id=$userId&offer_id=$offerId';
  }

  @override
  Future<bool> verifyCompletion(String completionId) async {
    // Implementar verificación específica de AdGate
    return true;
  }

  @override
  Future<void> handleCallback(Map<String, dynamic> callbackData) async {
    // Procesar callback específico de AdGate Media
    final userId = callbackData['subId'];
    final offerId = callbackData['offerId'];
    final reward = double.parse(callbackData['points'].toString()) / 100; // Convertir puntos a USD

    await _processCompletion(userId, offerId, reward, callbackData);
  }
}
```

## Flujo de Trabajo Detallado

### 1. Inicialización del Sistema
```dart
// En main.dart o durante la inicialización de la app
await OfferwallService.initialize();
await OfferwallService.syncOffersFromProviders();
```

### 2. Visualización de Ofertas para el Usuario
```dart
// En offerwall_screen.dart
class OfferwallScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final offers = ref.watch(availableOffersProvider);
    final userEarnings = ref.watch(offerwallEarningsProvider);

    return Scaffold(
      appBar: AppBar(title: Text('Ofertas Disponibles')),
      body: Column(
        children: [
          // Resumen de ganancias
          OfferwallEarningsSummary(earnings: userEarnings),

          // Filtros
          OfferwallFilters(),

          // Lista de ofertas
          Expanded(
            child: offers.when(
              data: (offerList) => OffersList(offers: offerList),
              loading: () => CircularProgressIndicator(),
              error: (error, stack) => ErrorWidget(error),
            ),
          ),
        ],
      ),
    );
  }
}
```

### 3. Completación de Oferta
```dart
// Flujo completo de completación
Future<void> completeOfferFlow(String userId, String offerId) async {
  try {
    // 1. Registrar inicio de oferta
    final completionId = await OfferwallService.startOffer(userId, offerId);

    // 2. Generar URL de la oferta
    final offerUrl = await OfferwallService.generateOfferUrl(userId, offerId);

    // 3. Abrir oferta en iframe o nueva ventana
    await _openOffer(offerUrl);

    // 4. Esperar callback del proveedor (automático)
    // 5. Verificar completación (automático)
    // 6. Actualizar ganancias del usuario (automático)

  } catch (e) {
    print('Error completing offer: $e');
    // Manejar error apropiadamente
  }
}
```

### 4. Manejo de Callbacks de Proveedores
```dart
// En Firebase Functions o endpoint dedicado
@HttpFunction()
Future<void> handleOfferwallCallback(HttpRequest request) async {
  final provider = request.uri.pathSegments.last; // 'offertoro', 'adgate', etc.
  final data = await request.body.transform(utf8.decoder).join();
  final callbackData = json.decode(data);

  // Validar signature del proveedor
  final isValid = await OfferwallService.validateCallback(provider, callbackData, request.headers['signature']);

  if (isValid) {
    await OfferwallService.handleProviderCallback(provider, callbackData);
    return Response.ok('OK');
  } else {
    return Response.forbidden('Invalid signature');
  }
}
```

---

**Nota**: Este documento será actualizado conforme avance la implementación del feature.
