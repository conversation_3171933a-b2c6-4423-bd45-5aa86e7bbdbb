{"name": "functions", "version": "1.0.0", "description": "Firebase Functions for PP Analytics Reward", "main": "index.js", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^11.9.0", "firebase-functions": "^4.4.0"}, "private": true, "devDependencies": {"firebase-tools": "^14.4.0"}}