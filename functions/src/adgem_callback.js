const functions = require('firebase-functions');
const admin = require('firebase-admin');
const crypto = require('crypto');

// Inicializar Firebase Admin si no está inicializado
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

// Configuración de AdGem
const ADGEM_CONFIG = {
  POSTBACK_KEY: 'hhha9f9g64h807ackda78f65',
  APP_ID: '30519',
  MAX_RETRIES: 3,
};

/**
 * Valida la firma de la petición AdGem
 * @param {Object} params - Parámetros de la petición
 * @returns {boolean} - true si la firma es válida
 */
function validateSignature(params) {
  const { signature, ...otherParams } = params;
  if (!signature) return false;
  
  // Ordenar parámetros alfabéticamente y crear string para firma
  const sortedParams = Object.keys(otherParams)
    .sort()
    .map(key => `${key}=${otherParams[key]}`)
    .join('&');
  
  // Generar firma usando el Postback Key
  const expectedSignature = crypto
    .createHash('md5')
    .update(`${sortedParams}${ADGEM_CONFIG.POSTBACK_KEY}`)
    .digest('hex');
  
  return signature === expectedSignature;
}

/**
 * Endpoint para recibir callbacks de AdGem
 * 
 * AdGem enviará una petición GET a este endpoint cuando un usuario complete una oferta.
 * Los parámetros se envían como query parameters en la URL.
 */
exports.adgemCallback = functions.https.onRequest(async (req, res) => {
  // Configurar CORS
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST');
  res.set('Access-Control-Allow-Headers', 'Content-Type');

  // Manejar preflight requests
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    console.log('AdGem Callback recibido:', req.query);
    
    // Extraer parámetros del query string
    const params = req.query;
    
    // Validar firma
    if (!validateSignature(params)) {
      console.error('Firma inválida en la petición AdGem');
      res.status(401).send('0');
      return;
    }
    
    // Validar parámetros requeridos
    const requiredParams = ['appid', 'playerid', 'offerid', 'amount', 'transactionid'];
    for (const param of requiredParams) {
      if (!params[param]) {
        console.error(`Parámetro requerido faltante: ${param}`);
        res.status(400).send('0');
        return;
      }
    }
    
    // Validar que el appid sea correcto
    if (params.appid !== ADGEM_CONFIG.APP_ID) {
      console.error(`App ID incorrecto: ${params.appid}`);
      res.status(400).send('0');
      return;
    }

    // Verificar si la transacción ya fue procesada (evitar duplicados)
    const existingTransaction = await db
      .collection('projects/pinataparty/offerwall/completions')
      .where('transactionId', '==', params.transactionid)
      .limit(1)
      .get();
    
    if (!existingTransaction.empty) {
      console.log(`Transacción duplicada: ${params.transactionid}`);
      res.status(200).send('1'); // Ya procesada, retornar éxito
      return;
    }
    
    // Calcular la recompensa en USD
    const amount = parseFloat(params.amount) || 0;
    const payout = parseFloat(params.payout) || 0;
    const rewardAmount = payout > 0 ? payout : amount / 100; // Convertir puntos a USD si es necesario
    
    // Procesar la completación de la oferta
    const completionData = {
      userId: params.playerid,
      offerId: params.offerid,
      provider: 'AdGem',
      startedAt: admin.firestore.FieldValue.serverTimestamp(),
      completedAt: admin.firestore.FieldValue.serverTimestamp(),
      verifiedAt: admin.firestore.FieldValue.serverTimestamp(),
      status: 'verified',
      rewardAmount: rewardAmount,
      currency: 'USD',
      transactionId: params.transactionid,
      providerData: {
        appId: params.appid,
        amount: amount,
        payout: payout,
        country: params.country || null,
        ip: params.ip || null,
        conversionDateTime: params.datetime || null,
        c1: params.c1 || null,
        c2: params.c2 || null,
        signature: params.signature || null,
      },
      retryCount: 0,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
    };
    
    // Guardar la completación usando una transacción para asegurar consistencia
    await db.runTransaction(async (transaction) => {
      // Guardar la completación
      const completionRef = db
        .collection('projects/pinataparty/offerwall/completions')
        .doc(params.transactionid);
      transaction.set(completionRef, completionData);
      
      // Actualizar las ganancias del usuario
      const earningsRef = db
        .collection('projects/pinataparty/offerwall/earnings')
        .doc(params.playerid);
      
      const earningsDoc = await transaction.get(earningsRef);
      
      if (earningsDoc.exists) {
        // Actualizar ganancias existentes
        const currentData = earningsDoc.data();
        transaction.update(earningsRef, {
          totalEarnings: (currentData.totalEarnings || 0) + rewardAmount,
          availableEarnings: (currentData.availableEarnings || 0) + rewardAmount,
          completedOffers: (currentData.completedOffers || 0) + 1,
          lastOfferDate: admin.firestore.FieldValue.serverTimestamp(),
          'earningsByProvider.AdGem': ((currentData.earningsByProvider && currentData.earningsByProvider.AdGem) || 0) + rewardAmount,
          lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
        });
      } else {
        // Crear nuevo documento de ganancias
        transaction.set(earningsRef, {
          totalEarnings: rewardAmount,
          availableEarnings: rewardAmount,
          pendingEarnings: 0,
          completedOffers: 1,
          pendingOffers: 0,
          rejectedOffers: 0,
          lastOfferDate: admin.firestore.FieldValue.serverTimestamp(),
          earningsByProvider: { AdGem: rewardAmount },
          offersByCategory: {},
          averageRewardPerOffer: rewardAmount,
          lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
      
      // Actualizar el perfil principal del usuario
      const userRef = db.collection('projects/pinataparty/users').doc(params.playerid);
      const userDoc = await transaction.get(userRef);
      
      if (userDoc.exists) {
        const currentData = userDoc.data();
        transaction.update(userRef, {
          offerwallEarnings: (currentData.offerwallEarnings || 0) + rewardAmount,
          lastOfferwallEarning: rewardAmount,
          lastOfferwallDate: admin.firestore.FieldValue.serverTimestamp(),
          lastUpdateAt: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
      
      // Registrar la transacción para auditoría
      const transactionRef = db
        .collection('projects/pinataparty/offerwall/transactions')
        .doc(params.transactionid);
      transaction.set(transactionRef, {
        transactionId: params.transactionid,
        provider: 'AdGem',
        userId: params.playerid,
        offerId: params.offerid,
        amount: amount,
        payout: payout,
        country: params.country || null,
        ip: params.ip || null,
        conversionDateTime: params.datetime || null,
        processedAt: admin.firestore.FieldValue.serverTimestamp(),
        status: 'completed',
        signature: params.signature || null,
      });
    });
    
    console.log(`AdGem Callback procesado exitosamente para usuario: ${params.playerid}, recompensa: $${rewardAmount}`);
    
    // AdGem espera "1" para indicar éxito
    res.status(200).send('1');
    
  } catch (error) {
    console.error('Error procesando AdGem callback:', error);
    
    // Intentar registrar el error
    try {
      await db.collection('projects/pinataparty/offerwall/errors').add({
        provider: 'AdGem',
        error: error.message,
        stack: error.stack,
        params: req.query,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });
    } catch (logError) {
      console.error('Error logging callback error:', logError);
    }
    
    res.status(500).send('0'); // AdGem espera "0" para indicar error
  }
});
