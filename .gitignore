# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# Node.js
node_modules/
package-lock.json
yarn.lock

# Firebase
.firebase/
.firebaserc

# IDE
.vscode/
.idea/
*.iml
*.ipr
*.iws

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE - IntelliJ
.idea/
*.iml
*.ipr
*.iws

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IDE - General
.classpath
.project
.settings/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
.fvm/

# Flutter Web
web/__deploy_assets/
web/version.json

# Coverage
coverage/
*.lcov

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local Netlify folder
.netlify

# Web related

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Firebase configuration files
google-services.json
GoogleService-Info.plist
firebase_options.dart

# Environment files
.env* 
