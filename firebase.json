{"hosting": {"target": "ppanaliticreward", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "functions": {"source": "functions"}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "flutter": {"platforms": {"dart": {"lib/firebase_options.dart": {"projectId": "portafoliodev-bd7f3", "configurations": {"web": "1:715605817338:web:c8d6fc9e845d4edb9f5643"}}}}}}