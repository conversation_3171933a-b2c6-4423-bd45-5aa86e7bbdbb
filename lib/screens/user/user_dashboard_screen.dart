import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../providers/user_provider.dart';
import '../../widgets/common/stats_card.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_display.dart';
import 'user_profile_screen.dart';
import '../../models/withdrawal_management.dart';
import '../../screens/home_screen.dart';
import '../../services/earnings_distribution_service.dart';

class UserDashboardScreen extends ConsumerStatefulWidget {
  const UserDashboardScreen({super.key});

  @override
  ConsumerState<UserDashboardScreen> createState() =>
      _UserDashboardScreenState();
}

class _UserDashboardScreenState extends ConsumerState<UserDashboardScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _shimmerAnimation;
  Timer? _shimmerTimer;
  bool _showShimmer = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    
    _shimmerAnimation = ColorTween(
      begin: Colors.amber[200],
      end: Colors.amber[700],
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    // Start the shimmer timer
    _startShimmerTimer();
    
    // Refresh data when screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.invalidate(userProfileProvider);

      // Actualizar ganancias estimadas cuando se carga el dashboard
      final userProfile = ref.read(userProfileProvider).valueOrNull;
      if (userProfile != null) {
        ref
            .read(earningsDistributionServiceProvider)
            .updateEstimatedEarnings(userProfile);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final userProfileAsync = ref.watch(userProfileProvider);
    // Formato de moneda con 4 decimales para mostrar valores pequeños como 0.002
    final currencyFormat =
        NumberFormat.currency(symbol: '\$', decimalDigits: 4);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Mi Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.person),
            tooltip: 'Mi Perfil',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const UserProfileScreen()),
              );
            },
          ),
        ],
      ),
      body: userProfileAsync.when(
        data: (userProfile) {
          if (userProfile == null) {
            return const Center(
              child: Text('No se pudo cargar el perfil de usuario'),
            );
          }

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tarjeta de bienvenida
                  Card(
                    elevation: 4,
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '¡Hola, ${userProfile.email.split('@')[0]}!',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Bienvenido a tu panel de recompensas',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 12),
                          Center(
                            child: InkWell(
                              onTap: () {
                                // TODO: Navigate to rewards screen
                              },
                              borderRadius: BorderRadius.circular(20),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 10,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.amber.shade700,
                                    width: 1.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.amber.withOpacity(0.1),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.diamond,
                                      color: Colors.amber.shade700,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    AnimatedBuilder(
                                      animation: _animationController,
                                      builder: (context, child) {
                                        return Text(
                                          'SweetGems: ${userProfile.offerwallEarnings?.toStringAsFixed(2) ?? '0.00'}',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: _showShimmer 
                                                ? _shimmerAnimation.value 
                                                : Colors.amber.shade800,
                                            shadows: _showShimmer 
                                                ? [
                                                    Shadow(
                                                      color: _shimmerAnimation.value ?? Colors.amber,
                                                      blurRadius: 10,
                                                    )
                                                  ]
                                                : null,
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Sección de ganancias
                  const Text(
                    'Mis Ganancias',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: StatsCard(
                          title: 'Ganancias Totales',
                          value: currencyFormat.format(userProfile.adEarnings),
                          icon: Icons.attach_money,
                          color: Colors.blue[700],
                          isCentered: true,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: StatsCard(
                          title: 'Disponible para Retirar',
                          value: currencyFormat.format(userProfile.availableAdEarnings),
                          icon: Icons.account_balance_wallet,
                          color: Colors.green[700],
                          isCentered: true,
                        ),
                      ),
                    ],
                  ),

                  // Ganancias estimadas
                  const SizedBox(height: 16),
                  const SizedBox(height: 16),
                  StatsCard(
                    title: 'Ganancias Estimadas',
                    value: currencyFormat.format(userProfile.estimatedEarnings),
                    subtitle: 'Pendientes de confirmar en la próxima distribución',
                    icon: Icons.pending_outlined,
                    color: Colors.amber[700],
                    backgroundColor: Colors.grey[850],
                    isCentered: true,
                  ),
                  const SizedBox(height: 32),

                  // Sección de información de retiro
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.account_balance_wallet,
                                  color: Colors.blue),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Información de Retiro',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Disponible para retirar: ${currencyFormat.format(userProfile.availableAdEarnings)}',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                          const SizedBox(height: 16),
                          Center(
                            child: OutlinedButton.icon(
                              icon: const Icon(Icons.arrow_forward),
                              label: const Text('Ir a Retiros'),
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                textStyle: const TextStyle(fontSize: 16),
                              ),
                              onPressed: () {
                                // Navegar a la pantalla de retiros
                                Navigator.of(context).pushNamed('/withdrawals');
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Sección de Sistema de Referidos
                  const Text(
                    'Sistema de Referidos',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          StatsCard(
                            title: 'Amigos Referidos',
                            value: userProfile.referralCount.toString(),
                            icon: Icons.people,
                            color: Colors.teal[700],
                            isCentered: true,
                          ),
                          const SizedBox(height: 16),
                          _buildInfoRow(
                            'Código de Referido',
                            userProfile.referralCode ?? 'No disponible',
                            userProfile.referralCode == null
                                ? Colors.orange
                                : Colors.green,
                          ),
                          const Divider(),
                          _buildInfoRow(
                            'Referido por',
                            userProfile.referredBy ?? 'No referido',
                            userProfile.referredBy == null ? Colors.grey : null,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Sección de información de cuenta
                  const Text(
                    'Información de Cuenta',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildInfoRow('Email', userProfile.email),
                          const Divider(),
                          _buildInfoRow('País', userProfile.country),
                          const Divider(),
                          _buildInfoRow(
                            'Teléfono',
                            userProfile.phoneNumber ?? 'No registrado',
                            userProfile.phoneNumber == null
                                ? Colors.orange
                                : null,
                          ),
                          const SizedBox(height: 16),
                          Center(
                            child: OutlinedButton.icon(
                              icon: const Icon(Icons.edit),
                              label: const Text('Editar Perfil'),
                              onPressed: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          const UserProfileScreen()),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => const LoadingIndicator(message: 'Cargando datos...'),
        error: (error, stack) => ErrorDisplay(
          message: 'Error al cargar datos: $error',
          onRetry: () => ref.invalidate(userProfileProvider),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => ref.invalidate(userProfileProvider),
        tooltip: 'Refrescar datos',
        heroTag: 'userDashboardRefresh',
        child: const Icon(Icons.refresh),
      ),
    );
  }

  @override
  void dispose() {
    _shimmerTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startShimmerTimer() {
    _shimmerTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        setState(() {
          _showShimmer = true;
          _animationController.repeat(reverse: true);
        });
        
        // Stop shimmer after 2 seconds
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            setState(() {
              _showShimmer = false;
              _animationController.reset();
            });
          }
        });
      }
    });
  }

  Widget _buildInfoRow(String label, String value, [Color? valueColor]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(color: valueColor),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
