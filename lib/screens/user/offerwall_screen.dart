import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/offerwall/adgem_config.dart';
import '../../widgets/offerwall/adgem_iframe.dart';
import '../../models/offerwall/wannads_config.dart';
import '../../widgets/offerwall/wannads_iframe.dart';
import '../../models/offerwall/ayet_config.dart';
import '../../widgets/offerwall/ayet_iframe.dart';
import '../../providers/user_provider.dart';

/// Pantalla principal del Offerwall
///
/// Muestra el offerwall de AdGem integrado en un iframe, junto con
/// información de ganancias y estadísticas del usuario.
class OfferwallScreen extends ConsumerStatefulWidget {
  const OfferwallScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<OfferwallScreen> createState() => _OfferwallScreenState();
}

enum OfferwallProvider { adGem, wannads, ayet }

class _OfferwallScreenState extends ConsumerState<OfferwallScreen> {
  bool _isOfferwallLoaded = false;
  String? _loadError;
  OfferwallProvider _selectedProvider = OfferwallProvider.adGem; // Default to AdGem

  @override
  Widget build(BuildContext context) {
    final userAsync = ref.watch(userProfileProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Ofertas Disponibles'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isOfferwallLoaded = false;
                _loadError = null;
              });
            },
            tooltip: 'Recargar ofertas',
          ),
        ],
      ),
      body: userAsync.when(
        data: (userData) => _buildOfferwallContent(userData),
        loading: () => const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Cargando información del usuario...'),
            ],
          ),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red.shade400,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(
                'Error cargando datos del usuario',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.red.shade700,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: const TextStyle(color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(userProfileProvider),
                child: const Text('Reintentar'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOfferwallContent(dynamic userData) {
    return Column(
      children: [
        // Selector de proveedor
        _buildProviderSelector(),
        const SizedBox(height: 16),

        // Iframe del offerwall
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: _buildOfferwallIframe(userData),
          ),
        ),
      ],
    );
  }

  Widget _buildProviderSelector() {
    final screenWidth = MediaQuery.of(context).size.width;
    // Calculate button width, considering padding and gaps between buttons
    final double buttonWidth = (screenWidth - 16 * 2 - 8 * 2) / 3; // 16*2 for L/R padding, 8*2 for gaps between 3 buttons

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: ToggleButtons(
        isSelected: List<bool>.generate(3, (index) => _selectedProvider.index == index),
        onPressed: (int index) {
          setState(() {
            _selectedProvider = OfferwallProvider.values[index];
            _isOfferwallLoaded = false; // Reset load state on provider switch
            _loadError = null;
          });
        },
        borderRadius: BorderRadius.circular(12),
        borderWidth: 1.5,
        borderColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
        selectedBorderColor: Theme.of(context).colorScheme.primary,
        selectedColor: Colors.white,
        fillColor: Theme.of(context).colorScheme.primary,
        color: Theme.of(context).colorScheme.primary,
        splashColor: Theme.of(context).colorScheme.primary.withOpacity(0.12),
        hoverColor: Theme.of(context).colorScheme.primary.withOpacity(0.04),
        constraints: BoxConstraints(
          minHeight: 48.0, // Increased height
          minWidth: buttonWidth, // Ensure buttons take up available space
        ),
        children: [
          _buildProviderButtonContent('AdGem', Icons.monetization_on_outlined),
          _buildProviderButtonContent('Wannads', Icons.card_giftcard_outlined),
          _buildProviderButtonContent('Ayet', Icons.stars_outlined),
        ],
      ),
    );
  }

  Widget _buildProviderButtonContent(String text, IconData icon) {
    final screenWidth = MediaQuery.of(context).size.width;
    final double buttonContentWidth = (screenWidth - 16 * 2 - 8 * 3 - 1.5*6) / 3; // screenwidth - L/R padding - gaps - borders

    return SizedBox(
      width: buttonContentWidth,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0), // Adjusted padding
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 20.0), // Icon added
            const SizedBox(width: 8.0),
            Text(text, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
          ],
        ),
      ),
    );
  }

  Widget _buildOfferwallIframe(dynamic userData) {
    if (_selectedProvider == OfferwallProvider.adGem) {
      return AdGemIframe(
        userId: userData.id ?? 'unknown_user',
        config: AdGemConfig.defaultConfig,
        height: double.infinity, 
        onLoad: () {
          if (mounted && _selectedProvider == OfferwallProvider.adGem) {
            setState(() {
              _isOfferwallLoaded = true;
              _loadError = null;
            });
          }
          print('AdGem Offerwall loaded successfully (from OfferwallScreen)');
        },
        onError: (error) {
          if (mounted && _selectedProvider == OfferwallProvider.adGem) {
            setState(() {
              _isOfferwallLoaded = false;
              _loadError = error;
            });
          }
          print('AdGem Offerwall error (from OfferwallScreen): $error');
        },
        additionalParams: {
          'country': userData.country ?? '',
        },
      );
    } else if (_selectedProvider == OfferwallProvider.wannads) {
      return WannadsIframe(
        userId: userData.id ?? 'unknown_user',
        config: WannadsConfig.defaultConfig,
        height: double.infinity,
        onLoad: () {
          if (mounted && _selectedProvider == OfferwallProvider.wannads) {
            setState(() {
              _isOfferwallLoaded = true;
              _loadError = null;
            });
          }
          print('Wannads Offerwall loaded successfully (from OfferwallScreen)');
        },
        onError: (error) {
          if (mounted && _selectedProvider == OfferwallProvider.wannads) {
            setState(() {
              _isOfferwallLoaded = false;
              _loadError = error;
            });
          }
          print('Wannads Offerwall error (from OfferwallScreen): $error');
        },
      );
    } else if (_selectedProvider == OfferwallProvider.ayet) {
      return AyetIframe(
        userId: userData.id ?? 'unknown_user',
        config: AyetConfig(
          adSlotId: '20291', // ID de tu oferta en AyetStudios
          // Parámetros personalizados
          custom1: 'sweegem', // Identificador de moneda
          custom2: '${userData.id}_${DateTime.now().millisecondsSinceEpoch}', // ID único de sesión
        ),
        height: double.infinity,
        onLoad: () {
          if (mounted && _selectedProvider == OfferwallProvider.ayet) {
            setState(() {
              _isOfferwallLoaded = true;
              _loadError = null;
            });
          }
          print('AyetStudios Offerwall loaded successfully');
        },
        onError: (error) {
          if (mounted && _selectedProvider == OfferwallProvider.ayet) {
            setState(() {
              _isOfferwallLoaded = false;
              _loadError = error;
            });
          }
          print('AyetStudios Offerwall error: $error');
        },
      );
    }
    // Fallback if no provider is selected, though this shouldn't happen with a default.
    return const Center(child: Text('Por favor, seleccione un proveedor.'));
  }
}
