import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../providers/user_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_display.dart';
import 'user_profile_screen.dart';

class UserWithdrawalsScreen extends ConsumerStatefulWidget {
  const UserWithdrawalsScreen({super.key});

  @override
  ConsumerState<UserWithdrawalsScreen> createState() =>
      _UserWithdrawalsScreenState();
}

class _UserWithdrawalsScreenState extends ConsumerState<UserWithdrawalsScreen> {
  @override
  void initState() {
    super.initState();
    // Refresh data when screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.invalidate(userProfileProvider);
      ref.invalidate(userWithdrawalsProvider);
    });
  }

  @override
  Widget build(BuildContext context) {
    final userProfileAsync = ref.watch(userProfileProvider);
    final userWithdrawalsAsync = ref.watch(userWithdrawalsProvider);
    final currencyFormat = NumberFormat.currency(symbol: '\$');
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Solicitar Retiro'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we came from a route or from the home screen
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              // If we can't pop, we're in the home screen's IndexedStack
              // Navigate to the home screen
              Navigator.of(context).pushReplacementNamed('/');
            }
          },
        ),
      ),
      body: userProfileAsync.when(
        data: (userProfile) {
          if (userProfile == null) {
            return const Center(
              child: Text('No se pudo cargar el perfil de usuario'),
            );
          }

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Solicitar Retiro',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.info_outline,
                                  color: Colors.blue),
                              const SizedBox(width: 8),
                              const Text(
                                'Información de Retiro',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Text(
                              'Mínimo para retirar: ${currencyFormat.format(10.0)}'),
                          const SizedBox(height: 8),
                          Text(
                              'Disponible para retirar: ${currencyFormat.format(userProfile.availableAdEarnings)}'),
                          const SizedBox(height: 16),
                          LinearProgressIndicator(
                            value: userProfile.availableAdEarnings >= 10.0
                                ? 1.0
                                : userProfile.availableAdEarnings / 10.0,
                            backgroundColor: Colors.grey[200],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              userProfile.availableAdEarnings >= 10.0
                                  ? Colors.green
                                  : Colors.orange,
                            ),
                            minHeight: 8,
                          ),
                          const SizedBox(height: 24),
                          // Verificar si el usuario tiene email verificado
                          if (!userProfile.isEmailVerified) ...[
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.red[100],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.red[300]!),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(Icons.warning_amber_rounded,
                                          color: Colors.red[700]),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'Verificación requerida',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.red[700],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                      '• Tu correo electrónico no está verificado',
                                      style: TextStyle(color: Colors.red[700])),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Para poder realizar retiros, debes verificar tu correo electrónico en la sección de Perfil.',
                                    style: TextStyle(
                                        color: Colors.red[700], fontSize: 12),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),
                            Center(
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.person),
                                label: const Text('Ir a mi Perfil'),
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                  textStyle: const TextStyle(fontSize: 16),
                                ),
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            const UserProfileScreen()),
                                  );
                                },
                              ),
                            ),
                          ] else
                            Center(
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.monetization_on),
                                label: const Text('Solicitar Retiro'),
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                  textStyle: const TextStyle(fontSize: 16),
                                ),
                                onPressed:
                                    userProfile.availableAdEarnings >= 10.0
                                        ? () => _showImprovedWithdrawalDialog(context,
                                            userProfile.availableAdEarnings)
                                        : null,
                              ),
                            ),
                          if (userProfile.availableAdEarnings < 10.0)
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Center(
                                child: Text(
                                  'Necesitas ${currencyFormat.format(10.0 - userProfile.availableAdEarnings)} más para poder retirar',
                                  style: TextStyle(
                                      color: Colors.grey[600], fontSize: 12),
                                ),
                              ),
                            ),

                          // Información sobre el proceso de aprobación
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 8),
                          const Text(
                            'Información importante:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            '• Las solicitudes de retiro deben ser aprobadas por un administrador',
                            style: TextStyle(fontSize: 12),
                          ),
                          const Text(
                            '• El proceso puede tardar hasta 48 horas hábiles',
                            style: TextStyle(fontSize: 12),
                          ),
                          const Text(
                            '• Recibirás una notificación cuando tu retiro sea procesado',
                            style: TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    'Solicitudes Pendientes',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 300, // Altura fija para la lista de solicitudes
                    child: userWithdrawalsAsync.when(
                      data: (withdrawals) {
                        final pendingWithdrawals = withdrawals
                            .where((w) => w['status'] == 'pending')
                            .toList();

                        if (pendingWithdrawals.isEmpty) {
                          return const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.check_circle_outline,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'No tienes solicitudes pendientes',
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }

                        return ListView.builder(
                          itemCount: pendingWithdrawals.length,
                          itemBuilder: (context, index) {
                            final withdrawal = pendingWithdrawals[index];
                            final requestDate =
                                withdrawal['requestDate'] as DateTime;
                            final amount =
                                (withdrawal['amount'] as num).toDouble();
                            final type = withdrawal['type'] as String;
                            final details =
                                withdrawal['details'] as Map<String, dynamic>;

                            String typeDisplay = type == 'mobile_recharge'
                                ? 'Recarga Móvil'
                                : 'Tarjeta de Regalo';
                            String optionDisplay =
                                details['option'] as String? ?? '';

                            return Card(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          type == 'mobile_recharge'
                                              ? Icons.phone_android
                                              : Icons.card_giftcard,
                                          color: Colors.blue,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          '$typeDisplay - $optionDisplay',
                                          style: const TextStyle(
                                              fontWeight: FontWeight.bold),
                                        ),
                                        const Spacer(),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: Colors.orange[100],
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(Icons.pending,
                                                  size: 16,
                                                  color: Colors.orange[800]),
                                              const SizedBox(width: 4),
                                              Text(
                                                'Pendiente',
                                                style: TextStyle(
                                                    color: Colors.orange[800],
                                                    fontSize: 12),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Text('Monto'),
                                              Text(
                                                currencyFormat.format(amount),
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Text('Fecha de solicitud'),
                                              Text(dateFormat
                                                  .format(requestDate)),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    const Text(
                                      'Tu solicitud está siendo procesada. Este proceso puede tardar hasta 48 horas hábiles.',
                                      style: TextStyle(
                                          fontSize: 12,
                                          fontStyle: FontStyle.italic),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      },
                      loading: () => const LoadingIndicator(
                          message: 'Cargando solicitudes...'),
                      error: (error, stack) => ErrorDisplay(
                        message: 'Error al cargar solicitudes: $error',
                        onRetry: () => ref.invalidate(userWithdrawalsProvider),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => const LoadingIndicator(message: 'Cargando datos...'),
        error: (error, stack) => ErrorDisplay(
          message: 'Error al cargar datos: $error',
          onRetry: () => ref.invalidate(userProfileProvider),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          ref.invalidate(userProfileProvider);
          ref.invalidate(userWithdrawalsProvider);
        },
        tooltip: 'Refrescar datos',
        heroTag: 'userWithdrawalsRefresh',
        child: const Icon(Icons.refresh),
      ),
    );
  }

  // Método para mostrar el diálogo de retiro con mejor diseño
  void _showImprovedWithdrawalDialog(BuildContext context, double availableAmount) {
    // Obtener el perfil del usuario actual
    final userProfile = ref.read(userProfileProvider).value;
    if (userProfile == null) return;

    final amountController = TextEditingController(text: availableAmount.toString());
    final formKey = GlobalKey<FormState>();
    // Solo permitir recarga móvil por ahora
    const String selectedMethod = 'mobile_recharge';
    String selectedOption = '';

    // Prellenar con el número de teléfono del usuario (solo recarga móvil por ahora)
    final detailsController = TextEditingController(text: userProfile.phoneNumber ?? '');

    // Opciones para cada tipo de retiro
    final Map<String, List<String>> typeOptions = {
      'mobile_recharge': ['Telcel', 'Movistar', 'AT&T'],
      'gift_card': ['Amazon', 'Google Play', 'iTunes'],
    };

    // Usar showDialog para mostrar el diálogo en el centro de la pantalla
    showDialog(
      context: context,
      barrierDismissible: false, // Evitar que se cierre al tocar fuera
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Obtener el tamaño de la pantalla
            final screenSize = MediaQuery.of(context).size;
            final maxWidth = screenSize.width > 600 ? 500.0 : screenSize.width * 0.9;
            final maxHeight = screenSize.height * 0.8; // Limitar altura al 80% de la pantalla

            return Center(
              child: Material(
                borderRadius: BorderRadius.circular(16),
                elevation: 24,
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: maxWidth,
                    maxHeight: maxHeight,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Encabezado
                        Container(
                          color: Theme.of(context).primaryColor,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Solicitar Retiro',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.close, color: Colors.white),
                                onPressed: () => Navigator.of(context).pop(),
                              ),
                            ],
                          ),
                        ),

                        // Contenido con scroll
                        Flexible(
                          child: SingleChildScrollView(
                            padding: const EdgeInsets.all(16),
                            child: Form(
                              key: formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Campo de cantidad
                                  TextFormField(
                                    controller: amountController,
                                    decoration: const InputDecoration(
                                      labelText: 'Cantidad a retirar',
                                      border: OutlineInputBorder(),
                                      prefixText: '\$',
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Por favor ingresa una cantidad';
                                      }
                                      final amount = double.tryParse(value);
                                      if (amount == null) {
                                        return 'Por favor ingresa un número válido';
                                      }
                                      if (amount < 10) {
                                        return 'La cantidad mínima es \$10';
                                      }
                                      if (amount > availableAmount) {
                                        return 'No tienes suficiente saldo disponible';
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(height: 16),

                                  // Campo de tipo de retiro
                                  DropdownButtonFormField<String>(
                                    value: selectedMethod,
                                    decoration: const InputDecoration(
                                      labelText: 'Tipo de retiro',
                                      border: OutlineInputBorder(),
                                    ),
                                    items: [
                                      DropdownMenuItem(
                                        value: 'mobile_recharge',
                                        child: Text('Recarga Móvil'),
                                      ),
                                      DropdownMenuItem(
                                        value: 'gift_card',
                                        enabled: false, // Deshabilitar esta opción
                                        child: Row(
                                          children: const [
                                            Text('Tarjeta de Regalo '),
                                            Text('(Próximamente)', style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic, color: Colors.grey)),
                                          ],
                                        ),
                                      ),
                                    ],
                                    onChanged: (value) {
                                      // Solo permitir cambiar a recarga móvil
                                      if (value != null && value == 'mobile_recharge') {
                                        setState(() {
                                          selectedOption = '';
                                          // Actualizar el campo de detalles con el número de teléfono
                                          detailsController.text = userProfile.phoneNumber ?? '';
                                        });
                                      }
                                    },
                                  ),
                                  const SizedBox(height: 16),

                                  // Campo de operador/plataforma
                                  DropdownButtonFormField<String>(
                                    value: selectedOption.isEmpty ? null : selectedOption,
                                    decoration: InputDecoration(
                                      labelText: selectedMethod == 'mobile_recharge'
                                          ? 'Operador'
                                          : 'Plataforma',
                                      border: const OutlineInputBorder(),
                                    ),
                                    hint: Text(selectedMethod == 'mobile_recharge'
                                        ? 'Selecciona un operador'
                                        : 'Selecciona una plataforma'),
                                    items: typeOptions[selectedMethod]?.map((option) {
                                          return DropdownMenuItem(
                                            value: option,
                                            child: Text(option),
                                          );
                                        }).toList() ??
                                        [],
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return selectedMethod == 'mobile_recharge'
                                            ? 'Por favor selecciona un operador'
                                            : 'Por favor selecciona una plataforma';
                                      }
                                      return null;
                                    },
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          selectedOption = value;
                                        });
                                      }
                                    },
                                  ),
                                  const SizedBox(height: 16),

                                  // Campo de detalles (teléfono/email)
                                  TextFormField(
                                    controller: detailsController,
                                    decoration: const InputDecoration(
                                      labelText: 'Número de teléfono',
                                      border: OutlineInputBorder(),
                                      hintText: 'Ingresa tu número de teléfono',
                                    ),
                                    keyboardType: TextInputType.phone,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Por favor ingresa un número de teléfono válido';
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(height: 24),
                                ],
                              ),
                            ),
                          ),
                        ),

                        // Botones de acción (siempre visibles en la parte inferior)
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Expanded(
                                child: OutlinedButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  style: OutlinedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(vertical: 12),
                                  ),
                                  child: const Text('Cancelar'),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Consumer(
                                  builder: (context, ref, child) {
                                    return ElevatedButton(
                                      onPressed: () async {
                                        if (formKey.currentState!.validate()) {
                                          Navigator.of(context).pop();

                                          // Mostrar indicador de carga
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            const SnackBar(
                                                content:
                                                    Text('Procesando solicitud de retiro...')),
                                          );

                                          // Solicitar retiro
                                          final amount = double.parse(amountController.text);
                                          final result =
                                              await ref.read(requestWithdrawalProvider((
                                            amount: amount,
                                            method: selectedMethod,
                                            details: {
                                              'option': selectedOption,
                                              'contactInfo': detailsController.text,
                                            },
                                          )).future);

                                          // Mostrar resultado
                                          if (context.mounted) {
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                  result == true
                                                      ? 'Solicitud de retiro enviada correctamente'
                                                      : 'Error al procesar la solicitud de retiro',
                                                ),
                                                backgroundColor:
                                                    result == true ? Colors.green : Colors.red,
                                              ),
                                            );
                                          }
                                        }
                                      },
                                      style: ElevatedButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(vertical: 12),
                                      ),
                                      child: const Text('Solicitar'),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
