import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/user_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_display.dart';

class UserProfileScreen extends ConsumerWidget {
  const UserProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfileAsync = ref.watch(userProfileProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Mi Perfil'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we came from a route or from the home screen
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              // If we can't pop, we're in the home screen's IndexedStack
              // Navigate to the home screen
              Navigator.of(context).pushReplacementNamed('/');
            }
          },
        ),
      ),
      body: userProfileAsync.when(
        data: (userProfile) {
          if (userProfile == null) {
            return const Center(
              child: Text('No se pudo cargar el perfil de usuario'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Información Personal',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                _buildInfoCard(
                  context,
                  title: 'Correo Electrónico',
                  value: userProfile.email,
                  icon: Icons.email,
                  onEdit: () => _showUpdateEmailDialog(context, ref, userProfile.email),
                  verified: userProfile.isEmailVerified,
                  onVerify: userProfile.isEmailVerified ? null : () => _showVerifyEmailDialog(context, ref),
                ),
                const SizedBox(height: 16),
                _buildInfoCard(
                  context,
                  title: 'Número de Teléfono',
                  value: userProfile.phoneNumber ?? 'No registrado',
                  icon: Icons.phone,
                  onEdit: () => _showUpdatePhoneDialog(context, ref, userProfile.phoneNumber),
                  // No verification status or button for phone number
                ),
                const SizedBox(height: 16),
                _buildInfoCard(
                  context,
                  title: 'País',
                  value: userProfile.country,
                  icon: Icons.public,
                  canEdit: false,
                ),
                const SizedBox(height: 16),
                _buildInfoCard(
                  context,
                  title: 'Fecha de Registro',
                  value: '${userProfile.createAt.day}/${userProfile.createAt.month}/${userProfile.createAt.year}',
                  icon: Icons.calendar_today,
                  canEdit: false,
                ),
              ],
            ),
          );
        },
        loading: () => const LoadingIndicator(message: 'Cargando perfil...'),
        error: (error, stack) => ErrorDisplay(
          message: 'Error al cargar perfil: $error',
          onRetry: () => ref.invalidate(userProfileProvider),
        ),
      ),
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    bool canEdit = true,
    Color? valueColor,
    VoidCallback? onEdit,
    bool? verified,
    VoidCallback? onVerify,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(icon, size: 28),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      if (verified != null) ...[
                        const SizedBox(width: 4),
                        Icon(
                          verified ? Icons.verified_user : Icons.warning,
                          color: verified ? Colors.green : Colors.orange,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          verified ? 'Verificado' : 'No verificado',
                          style: TextStyle(
                            fontSize: 12,
                            color: verified ? Colors.green : Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: valueColor,
                    ),
                  ),
                  if (onVerify != null) ...[
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: onVerify,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.verified_user, color: Colors.blue, size: 14),
                          const SizedBox(width: 4),
                          Text(
                            'Verificar ahora',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (canEdit)
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: onEdit,
                tooltip: 'Editar',
              ),
          ],
        ),
      ),
    );
  }

  void _showUpdateEmailDialog(BuildContext context, WidgetRef ref, String currentEmail) {
    final emailController = TextEditingController(text: currentEmail);
    final passwordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Actualizar Correo Electrónico'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: emailController,
                decoration: const InputDecoration(
                  labelText: 'Nuevo Correo Electrónico',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Por favor ingresa un correo electrónico';
                  }
                  if (!value.contains('@')) {
                    return 'Por favor ingresa un correo electrónico válido';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: passwordController,
                decoration: const InputDecoration(
                  labelText: 'Contraseña Actual',
                  border: OutlineInputBorder(),
                ),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Por favor ingresa tu contraseña actual';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          Consumer(
            builder: (context, ref, child) {
              return ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    Navigator.of(context).pop();

                    // Mostrar indicador de carga
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Actualizando correo electrónico...')),
                    );

                    // Actualizar correo electrónico
                    final result = await ref.read(updateEmailProvider((
                      email: emailController.text,
                      password: passwordController.text,
                    )).future);

                    // Mostrar resultado
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            result == true
                                ? 'Correo electrónico actualizado correctamente'
                                : 'Error al actualizar correo electrónico',
                          ),
                          backgroundColor: result == true ? Colors.green : Colors.red,
                        ),
                      );
                    }
                  }
                },
                child: const Text('Actualizar'),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showUpdatePhoneDialog(BuildContext context, WidgetRef ref, String? currentPhone) {
    final phoneController = TextEditingController(text: currentPhone);
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Actualizar Número de Teléfono'),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: phoneController,
            decoration: const InputDecoration(
              labelText: 'Número de Teléfono',
              border: OutlineInputBorder(),
              hintText: '+52 1234567890',
            ),
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Por favor ingresa un número de teléfono';
              }
              return null;
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          Consumer(
            builder: (context, ref, child) {
              return ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    Navigator.of(context).pop();

                    // Mostrar indicador de carga
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Actualizando número de teléfono...')),
                    );

                    // Actualizar número de teléfono
                    final result = await ref.read(updatePhoneProvider(phoneController.text));

                    // Mostrar resultado
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            result == true
                                ? 'Número de teléfono actualizado correctamente'
                                : 'Error al actualizar número de teléfono',
                          ),
                          backgroundColor: result == true ? Colors.green : Colors.red,
                        ),
                      );
                    }
                  }
                },
                child: const Text('Actualizar'),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showVerifyEmailDialog(BuildContext context, WidgetRef ref) {
    // Obtener el email del usuario actual
    final userProfile = ref.read(userProfileProvider).value;
    final email = userProfile?.email ?? '';

    showDialog(
      context: context,
      barrierDismissible: false, // No permitir cerrar el diálogo tocando fuera
      builder: (context) {
        return Consumer(
          builder: (context, ref, child) {
            final verificationState = ref.watch(emailVerificationStateProvider);

            // Si el estado es inicial, mostrar el estado inicial sin iniciar automáticamente
            // El usuario debe hacer clic en el botón para iniciar el proceso
            if (verificationState == EmailVerificationState.initial) {
              return AlertDialog(
                title: const Text('Verificar Correo Electrónico'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Vamos a enviar un correo de verificación a $email'),
                    const SizedBox(height: 16),
                    const Text(
                      'Haz clic en "Enviar Correo" para recibir un enlace de verificación. '
                      'Luego, revisa tu bandeja de entrada y haz clic en el enlace para verificar tu correo.',
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancelar'),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      // Iniciar el proceso de verificación
                      await ref.read(sendVerificationEmailProvider.future);
                    },
                    child: const Text('Enviar Correo'),
                  ),
                ],
              );
            } else if (verificationState == EmailVerificationState.sending) {
              return AlertDialog(
                title: const Text('Enviando Correo...'),
                content: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Enviando correo de verificación...'),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancelar'),
                  ),
                ],
              );
            } else if (verificationState == EmailVerificationState.sent) {
              return AlertDialog(
                title: const Text('Correo Enviado'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Se ha enviado un correo de verificación a $email'),
                    const SizedBox(height: 16),
                    const Text(
                      'Por favor, revisa tu bandeja de entrada y haz clic en el enlace para verificar tu correo. '
                      'Luego, haz clic en "Verificar" para confirmar que has completado el proceso.',
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancelar'),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      // Verificar si el correo ha sido verificado
                      await ref.read(checkEmailVerifiedProvider.future);
                    },
                    child: const Text('Verificar'),
                  ),
                ],
              );
            } else if (verificationState == EmailVerificationState.verified) {
              return AlertDialog(
                title: const Text('Correo Verificado'),
                content: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 60),
                    SizedBox(height: 16),
                    Text(
                      'Tu correo electrónico ha sido verificado correctamente',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                actions: [
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cerrar'),
                  ),
                ],
              );
            } else {
              // Estado de error
              return AlertDialog(
                title: const Text('Error de Verificación'),
                content: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.error, color: Colors.red, size: 60),
                    SizedBox(height: 16),
                    Text(
                      'No se pudo verificar tu correo electrónico. Por favor, inténtalo de nuevo.',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancelar'),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      // Iniciar el proceso de verificación
                      await ref.read(sendVerificationEmailProvider.future);
                    },
                    child: const Text('Enviar Correo'),
                  )
                ],
              );
            }
          },
        );
      },
    );
  }

  // Phone verification dialog has been removed as it's no longer needed
  void _showVerifyPhoneDialog(BuildContext context, WidgetRef ref, String phoneNumber) {
    // This method is kept for compatibility but does nothing now
    // Phone verification is no longer required
    return;
  }
}
