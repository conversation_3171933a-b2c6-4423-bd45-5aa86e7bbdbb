import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../providers/user_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_display.dart';

class UserHistoryScreen extends ConsumerStatefulWidget {
  const UserHistoryScreen({super.key});

  @override
  ConsumerState<UserHistoryScreen> createState() => _UserHistoryScreenState();
}

class _UserHistoryScreenState extends ConsumerState<UserHistoryScreen> {
  @override
  void initState() {
    super.initState();
    // Refresh data when screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.invalidate(userWithdrawalsProvider);
    });
  }

  @override
  Widget build(BuildContext context) {
    final userWithdrawalsAsync = ref.watch(userWithdrawalsProvider);
    final currencyFormat = NumberFormat.currency(symbol: '\$');
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Historial de Retiros'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we came from a route or from the home screen
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              // If we can't pop, we're in the home screen's IndexedStack
              // Navigate to the home screen
              Navigator.of(context).pushReplacementNamed('/');
            }
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Historial de Retiros',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Aquí puedes ver el historial completo de tus solicitudes de retiro',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: userWithdrawalsAsync.when(
                data: (withdrawals) {
                  if (withdrawals.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.history,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No tienes historial de retiros',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Cuando solicites un retiro, aparecerá aquí',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // Ordenar por fecha de solicitud (más reciente primero)
                  withdrawals.sort((a, b) => (b['requestDate'] as DateTime)
                      .compareTo(a['requestDate'] as DateTime));

                  return ListView.builder(
                    itemCount: withdrawals.length,
                    itemBuilder: (context, index) {
                      final withdrawal = withdrawals[index];
                      final requestDate = withdrawal['requestDate'] as DateTime;
                      final processedDate = withdrawal['processedDate'] as DateTime?;
                      final amount = (withdrawal['amount'] as num).toDouble();
                      final type = withdrawal['type'] as String;
                      final status = withdrawal['status'] as String;
                      final details = withdrawal['details'] as Map<String, dynamic>;
                      final rejectionReason = withdrawal['rejectionReason'] as String?;

                      String typeDisplay = type == 'mobile_recharge' ? 'Recarga Móvil' : 'Tarjeta de Regalo';
                      String optionDisplay = details['option'] as String? ?? '';
                      String contactInfo = details['contactInfo'] as String? ?? '';

                      Color statusColor;
                      IconData statusIcon;
                      String statusText;

                      switch (status) {
                        case 'pending':
                          statusColor = Colors.orange;
                          statusIcon = Icons.pending;
                          statusText = 'Pendiente';
                          break;
                        case 'approved':
                          statusColor = Colors.green;
                          statusIcon = Icons.check_circle;
                          statusText = 'Aprobado';
                          break;
                        case 'rejected':
                          statusColor = Colors.red;
                          statusIcon = Icons.cancel;
                          statusText = 'Rechazado';
                          break;
                        default:
                          statusColor = Colors.grey;
                          statusIcon = Icons.help_outline;
                          statusText = 'Desconocido';
                      }

                      return Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    type == 'mobile_recharge' ? Icons.phone_android : Icons.card_giftcard,
                                    color: Colors.blue,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '$typeDisplay - $optionDisplay',
                                    style: const TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  const Spacer(),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: statusColor.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(statusIcon, size: 16, color: statusColor),
                                        const SizedBox(width: 4),
                                        Text(
                                          statusText,
                                          style: TextStyle(color: statusColor, fontSize: 12),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const Text('Monto'),
                                        Text(
                                          currencyFormat.format(amount),
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const Text('Fecha de solicitud'),
                                        Text(dateFormat.format(requestDate)),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text('Contacto: $contactInfo'),
                              if (processedDate != null) ...[
                                const SizedBox(height: 8),
                                Text(
                                  'Procesado el: ${dateFormat.format(processedDate)}',
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ],
                              if (rejectionReason != null) ...[
                                const SizedBox(height: 8),
                                Text(
                                  'Motivo de rechazo: $rejectionReason',
                                  style: TextStyle(fontSize: 12, color: Colors.red[800]),
                                ),
                              ],
                              if (status == 'pending') ...[
                                const SizedBox(height: 8),
                                const Text(
                                  'Tu solicitud está siendo procesada. Este proceso puede tardar hasta 48 horas hábiles.',
                                  style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                                ),
                              ],
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
                loading: () => const LoadingIndicator(message: 'Cargando historial...'),
                error: (error, stack) => ErrorDisplay(
                  message: 'Error al cargar historial: $error',
                  onRetry: () => ref.invalidate(userWithdrawalsProvider),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => ref.invalidate(userWithdrawalsProvider),
        tooltip: 'Refrescar historial',
        heroTag: 'userHistoryRefresh',
        child: const Icon(Icons.refresh),
      ),
    );
  }
}
