import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../models/withdrawal_management.dart';
import '../../services/admin_service.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_display.dart';
import '../../widgets/common/index_error_display.dart';
import '../../providers/admin_provider.dart' hide DateTimeRange;

class WithdrawalManagementScreen extends ConsumerStatefulWidget {
  const WithdrawalManagementScreen({super.key});

  @override
  ConsumerState<WithdrawalManagementScreen> createState() =>
      _WithdrawalManagementScreenState();
}

class _WithdrawalManagementScreenState
    extends ConsumerState<WithdrawalManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Inicializar el rango de fechas al día actual
    final now = DateTime.now();
    _startDate = DateTime(now.year, now.month, now.day);
    _endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);

    // Cargar los retiros procesados inicialmente
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadProcessedWithdrawals();
    });
  }

  void _loadProcessedWithdrawals() {
    ref.read(processedWithdrawalsProvider.notifier).loadWithdrawals(
          status: _selectedStatus,
          startDate: _startDate,
          endDate: _endDate,
        );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _refreshData() {
    ref.invalidate(pendingWithdrawalsProvider);
    ref.read(processedWithdrawalsProvider.notifier).refresh();
  }

  // Verifica si dos fechas corresponden al mismo día
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  // Verifica si una fecha es hoy
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  Future<void> _approveWithdrawal(String withdrawalId) async {
    try {
      await AdminService.approveWithdrawal(withdrawalId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Retiro aprobado correctamente')),
        );
        _refreshData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _rejectWithdrawal(String withdrawalId) async {
    final reasonController = TextEditingController();
    String? reason;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rechazar Retiro'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Por favor, indica el motivo del rechazo:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Motivo',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              reason = reasonController.text;
              Navigator.pop(context);
            },
            child: const Text('Rechazar'),
          ),
        ],
      ),
    );

    if (reason != null && reason!.isNotEmpty) {
      try {
        await AdminService.rejectWithdrawal(withdrawalId, reason!);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Retiro rechazado correctamente')),
          );
          _refreshData();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${e.toString()}')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestión de Retiros'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Pendientes'),
            Tab(text: 'Historial'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Tab de retiros pendientes
          _buildPendingWithdrawalsTab(),

          // Tab de historial de retiros
          _buildProcessedWithdrawalsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _refreshData,
        tooltip: 'Refrescar',
        heroTag: 'withdrawalManagementRefresh',
        child: const Icon(Icons.refresh),
      ),
    );
  }

  Widget _buildPendingWithdrawalsTab() {
    final pendingWithdrawals = ref.watch(pendingWithdrawalsProvider);

    return pendingWithdrawals.when(
      data: (withdrawals) {
        if (withdrawals.isEmpty) {
          return const Center(
            child: Text('No hay retiros pendientes'),
          );
        }

        return ListView.builder(
          itemCount: withdrawals.length,
          itemBuilder: (context, index) {
            final withdrawal = withdrawals[index];
            return _buildWithdrawalCard(withdrawal, true);
          },
        );
      },
      loading: () =>
          const LoadingIndicator(message: 'Cargando retiros pendientes...'),
      error: (error, stack) {
        // Verificar si es un error de índice
        if (error.toString().contains('index')) {
          return IndexErrorDisplay(
            message:
                'Se requiere un índice compuesto para cargar los retiros pendientes.',
            indexDefinition: '''
{
  "collectionGroup": "withdrawals",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "status", "order": "ASCENDING" },
    { "fieldPath": "requestDate", "order": "DESCENDING" }
  ]
}
''',
            onRetry: () => ref.invalidate(pendingWithdrawalsProvider),
          );
        }

        // Otros errores
        return ErrorDisplay(
          message: 'Error al cargar retiros pendientes: $error',
          onRetry: () => ref.invalidate(pendingWithdrawalsProvider),
        );
      },
    );
  }

  Widget _buildProcessedWithdrawalsTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String?>(
                  value: _selectedStatus,
                  decoration: const InputDecoration(
                    labelText: 'Estado',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: null,
                      child: Text('Todos'),
                    ),
                    DropdownMenuItem(
                      value: 'approved',
                      child: Text('Aprobados'),
                    ),
                    DropdownMenuItem(
                      value: 'rejected',
                      child: Text('Rechazados'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value;
                    });
                    _loadProcessedWithdrawals();
                  },
                ),
              ),
              const SizedBox(width: 16),
              IconButton(
                icon: const Icon(Icons.date_range),
                onPressed: () async {
                  final DateTimeRange? dateRange = await showDateRangePicker(
                    context: context,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now(),
                    initialDateRange: _startDate != null && _endDate != null
                        ? DateTimeRange(start: _startDate!, end: _endDate!)
                        : null,
                  );

                  if (dateRange != null) {
                    setState(() {
                      _startDate = dateRange.start;
                      _endDate = DateTime(
                        dateRange.end.year,
                        dateRange.end.month,
                        dateRange.end.day,
                        23,
                        59,
                        59,
                      );
                    });
                    _loadProcessedWithdrawals();
                  }
                },
                tooltip: 'Seleccionar rango de fechas',
              ),
              if (_startDate != null && _endDate != null) ...[
                IconButton(
                  icon: const Icon(Icons.today),
                  onPressed: () {
                    setState(() {
                      final now = DateTime.now();
                      _startDate = DateTime(now.year, now.month, now.day);
                      _endDate =
                          DateTime(now.year, now.month, now.day, 23, 59, 59);
                    });
                    _loadProcessedWithdrawals();
                  },
                  tooltip: 'Mostrar solo hoy',
                ),
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    setState(() {
                      _startDate = null;
                      _endDate = null;
                    });
                    _loadProcessedWithdrawals();
                  },
                  tooltip: 'Limpiar fechas',
                ),
              ]
            ],
          ),
        ),
        if (_startDate != null && _endDate != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Text(
                  'Periodo: ${DateFormat('dd/MM/yyyy').format(_startDate!)} - ${DateFormat('dd/MM/yyyy').format(_endDate!)}',
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
                // Verificar si es solo el día actual
                if (_isSameDay(_startDate!, _endDate!) &&
                    _isToday(_startDate!)) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.today, size: 14, color: Colors.blue),
                        SizedBox(width: 4),
                        Text(
                          'HOY',
                          style: TextStyle(
                            color: Colors.blue,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              final processedWithdrawals =
                  ref.watch(processedWithdrawalsProvider);

              return processedWithdrawals.when(
                data: (withdrawals) {
                  if (withdrawals.isEmpty) {
                    return const Center(
                      child:
                          Text('No hay retiros procesados con estos filtros'),
                    );
                  }

                  return ListView.builder(
                    itemCount: withdrawals.length,
                    itemBuilder: (context, index) {
                      final withdrawal = withdrawals[index];
                      return _buildWithdrawalCard(withdrawal, false);
                    },
                  );
                },
                loading: () => const LoadingIndicator(
                    message: 'Cargando historial de retiros...'),
                error: (error, stack) {
                  // Verificar si es un error de índice
                  if (error.toString().contains('index')) {
                    return IndexErrorDisplay(
                      message:
                          'Se requiere un índice compuesto para cargar el historial de retiros.',
                      indexDefinition: '''
{
  "collectionGroup": "withdrawals",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "status", "order": "ASCENDING" },
    { "fieldPath": "requestDate", "order": "DESCENDING" }
  ]
}
''',
                      onRetry: () => ref
                          .read(processedWithdrawalsProvider.notifier)
                          .refresh(),
                    );
                  }

                  // Otros errores
                  return ErrorDisplay(
                    message: 'Error al cargar historial de retiros: $error',
                    onRetry: () => ref
                        .read(processedWithdrawalsProvider.notifier)
                        .refresh(),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildWithdrawalCard(WithdrawalManagement withdrawal, bool isPending) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    final currencyFormat = NumberFormat.currency(symbol: '\$');

    Color statusColor;
    IconData statusIcon;

    switch (withdrawal.status) {
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
      case 'approved':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    withdrawal.userEmail,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        withdrawal.status.toUpperCase(),
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Monto'),
                      Text(
                        currencyFormat.format(withdrawal.amount),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Tipo'),
                      Text(
                        withdrawal.type == 'mobile_recharge'
                            ? 'Recarga Móvil'
                            : 'Tarjeta de Regalo',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Fecha de Solicitud'),
                      Text(
                        dateFormat.format(withdrawal.requestDate),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text('Detalles: ${withdrawal.details.toString()}'),
            if (withdrawal.userPhone != null)
              Text('Teléfono: ${withdrawal.userPhone}'),
            Text('País: ${withdrawal.userCountry}'),
            if (!isPending && withdrawal.processedDate != null) ...[
              const SizedBox(height: 8),
              Text(
                  'Procesado: ${dateFormat.format(withdrawal.processedDate!)}'),
              if (withdrawal.processedBy != null)
                Text('Procesado por: ${withdrawal.processedBy}'),
              if (withdrawal.rejectionReason != null)
                Text('Motivo de rechazo: ${withdrawal.rejectionReason}'),
            ],
            if (isPending) ...[
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton.icon(
                    onPressed: () => _rejectWithdrawal(withdrawal.id),
                    icon: const Icon(Icons.cancel, color: Colors.red),
                    label: const Text('Rechazar'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: () => _approveWithdrawal(withdrawal.id),
                    icon: const Icon(Icons.check_circle),
                    label: const Text('Aprobar'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
