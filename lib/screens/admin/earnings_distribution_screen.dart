import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/models.dart';
import '../../services/earnings_distribution_service.dart';

/// Pantalla para gestionar las distribuciones de ganancias
class EarningsDistributionScreen extends ConsumerStatefulWidget {
  const EarningsDistributionScreen({Key? key}) : super(key: key);

  @override
  _EarningsDistributionScreenState createState() => _EarningsDistributionScreenState();
}

class _EarningsDistributionScreenState extends ConsumerState<EarningsDistributionScreen> {
  final _startDateController = TextEditingController();
  final _endDateController = TextEditingController();
  DateTime? _startDate;
  DateTime? _endDate;
  
  // Valores de eCPM por red y país
  final Map<String, Map<String, TextEditingController>> _ecpmControllers = {
    'AdMob': {
      'US': TextEditingController(text: '3.0'),
      'MX': TextEditingController(text: '1.5'),
      'default': TextEditingController(text: '1.0'),
    },
    'Unity': {
      'US': TextEditingController(text: '2.5'),
      'MX': TextEditingController(text: '1.2'),
      'default': TextEditingController(text: '0.8'),
    },
    'Meta': {
      'US': TextEditingController(text: '2.0'),
      'MX': TextEditingController(text: '1.0'),
      'default': TextEditingController(text: '0.6'),
    },
    'default': {
      'US': TextEditingController(text: '2.0'),
      'MX': TextEditingController(text: '1.0'),
      'default': TextEditingController(text: '0.8'),
    },
  };
  
  @override
  void dispose() {
    _startDateController.dispose();
    _endDateController.dispose();
    
    // Dispose de todos los controllers de eCPM
    for (var network in _ecpmControllers.keys) {
      for (var country in _ecpmControllers[network]!.keys) {
        _ecpmControllers[network]![country]!.dispose();
      }
    }
    
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final distributions = ref.watch(distributionsProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Distribución de Ganancias'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Nueva Distribución de Ganancias',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Selección de fechas
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _startDateController,
                    decoration: const InputDecoration(
                      labelText: 'Fecha de inicio',
                      border: OutlineInputBorder(),
                    ),
                    readOnly: true,
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _startDate ?? DateTime.now().subtract(const Duration(days: 15)),
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      
                      if (date != null) {
                        setState(() {
                          _startDate = date;
                          _startDateController.text = DateFormat('dd/MM/yyyy').format(date);
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _endDateController,
                    decoration: const InputDecoration(
                      labelText: 'Fecha de fin',
                      border: OutlineInputBorder(),
                    ),
                    readOnly: true,
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _endDate ?? DateTime.now(),
                        firstDate: _startDate ?? DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      
                      if (date != null) {
                        setState(() {
                          _endDate = date;
                          _endDateController.text = DateFormat('dd/MM/yyyy').format(date);
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Configuración de eCPM
            const Text(
              'Configuración de eCPM',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            
            // Tabla de eCPM
            DataTable(
              columns: const [
                DataColumn(label: Text('Red')),
                DataColumn(label: Text('US')),
                DataColumn(label: Text('MX')),
                DataColumn(label: Text('Otros')),
              ],
              rows: _ecpmControllers.keys.map((network) {
                return DataRow(
                  cells: [
                    DataCell(Text(network)),
                    DataCell(
                      TextField(
                        controller: _ecpmControllers[network]!['US'],
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        ),
                      ),
                    ),
                    DataCell(
                      TextField(
                        controller: _ecpmControllers[network]!['MX'],
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        ),
                      ),
                    ),
                    DataCell(
                      TextField(
                        controller: _ecpmControllers[network]!['default'],
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        ),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
            const SizedBox(height: 24),
            
            // Botón para crear distribución
            Center(
              child: ElevatedButton.icon(
                icon: const Icon(Icons.calculate),
                label: const Text('Crear y Procesar Distribución'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
                onPressed: _startDate != null && _endDate != null
                    ? () => _createAndProcessDistribution()
                    : null,
              ),
            ),
            const SizedBox(height: 32),
            
            // Historial de distribuciones
            const Text(
              'Historial de Distribuciones',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Tabla de historial
            Expanded(
              child: distributions.when(
                data: (distributionsList) {
                  if (distributionsList.isEmpty) {
                    return const Center(
                      child: Text('No hay distribuciones registradas'),
                    );
                  }
                  
                  return ListView.builder(
                    itemCount: distributionsList.length,
                    itemBuilder: (context, index) {
                      final distribution = distributionsList[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Distribución #${index + 1}',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Chip(
                                    label: Text(distribution.status),
                                    backgroundColor: _getStatusColor(distribution.status),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text('Período: ${DateFormat('dd/MM/yyyy').format(distribution.startDate)} - ${DateFormat('dd/MM/yyyy').format(distribution.endDate)}'),
                              Text('Fecha de distribución: ${DateFormat('dd/MM/yyyy HH:mm').format(distribution.distributionDate)}'),
                              Text('Administrador: ${distribution.adminEmail}'),
                              const SizedBox(height: 8),
                              Text('Anuncios procesados: ${distribution.totalProcessedAds}'),
                              Text('Ganancias distribuidas: \$${distribution.totalDistributedEarnings.toStringAsFixed(2)}'),
                              Text('Usuarios procesados: ${distribution.processedUserIds.length}'),
                              if (distribution.errorMessage != null) ...[
                                const SizedBox(height: 8),
                                Text(
                                  'Error: ${distribution.errorMessage}',
                                  style: const TextStyle(color: Colors.red),
                                ),
                              ],
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Text('Error: $error'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
  
  void _createAndProcessDistribution() async {
    if (_startDate == null || _endDate == null) return;
    
    // Mostrar diálogo de confirmación
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar Distribución'),
        content: const Text(
          'Esta acción calculará y distribuirá las ganancias para todos los usuarios en el período seleccionado. '
          '¿Está seguro de continuar?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );
    
    if (confirm != true) return;
    
    // Mostrar indicador de progreso
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Creando distribución...')),
    );
    
    try {
      // Convertir valores de eCPM
      Map<String, Map<String, double>> ecpmRates = {};
      
      for (var network in _ecpmControllers.keys) {
        ecpmRates[network] = {};
        
        for (var country in _ecpmControllers[network]!.keys) {
          final value = double.tryParse(_ecpmControllers[network]![country]!.text) ?? 0.0;
          ecpmRates[network]![country] = value;
        }
      }
      
      // Crear distribución
      final distributionId = await ref.read(earningsDistributionServiceProvider).createDistribution(
        startDate: _startDate!,
        endDate: _endDate!,
        ecpmRates: ecpmRates,
        adminId: FirebaseAuth.instance.currentUser!.uid,
        adminEmail: FirebaseAuth.instance.currentUser!.email!,
      );
      
      // Procesar distribución
      await ref.read(earningsDistributionServiceProvider).processDistribution(distributionId);
      
      // Actualizar lista de distribuciones
      ref.refresh(distributionsProvider);
      
      // Mostrar mensaje de éxito
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Distribución procesada correctamente'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Mostrar mensaje de error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
