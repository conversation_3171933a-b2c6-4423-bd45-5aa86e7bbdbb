import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../providers/analytics_provider.dart';
import '../../widgets/common/stats_card.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_display.dart';
import '../../widgets/charts/impressions_chart.dart';
import '../../widgets/charts/revenue_chart.dart';
import '../../services/firebase_service.dart';

class AdminDashboardScreen extends ConsumerWidget {
  const AdminDashboardScreen({super.key});

  // Método para verificar si el rango de fechas es solo el día actual
  bool _isToday(({DateTime startDate, DateTime endDate}) dateRange) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return dateRange.startDate.year == today.year &&
        dateRange.startDate.month == today.month &&
        dateRange.startDate.day == today.day &&
        dateRange.endDate.year == today.year &&
        dateRange.endDate.month == today.month &&
        dateRange.endDate.day == today.day;
  }

  // Método para construir la etiqueta del rango de fechas
  Widget _buildDateRangeLabel(
      ({DateTime startDate, DateTime endDate}) dateRange) {
    final dateFormat = DateFormat('dd/MM/yyyy');
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Verificar si el rango es solo el día actual
    if (_isToday(dateRange)) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Hoy (${dateFormat.format(today)})'),
          const SizedBox(width: 4),
          const Icon(Icons.today, size: 16, color: Colors.blue),
        ],
      );
    }

    // Si es otro rango de fechas
    return Text(
        '${dateFormat.format(dateRange.startDate)} - ${dateFormat.format(dateRange.endDate)}');
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsData = ref.watch(filteredAnalyticsDataProvider);
    final dateRange = ref.watch(dateRangeProvider);
    final selectedNetwork = ref.watch(selectedNetworkProvider);
    final selectedCountry = ref.watch(selectedCountryProvider);
    final loadingProgress = ref.watch(analyticsLoadingProgressProvider);

    final numberFormat = NumberFormat("#,##0");
    final currencyFormat = NumberFormat.currency(symbol: '\$');

    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: selectedNetwork,
                    decoration: const InputDecoration(
                      labelText: 'Red Publicitaria',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('Todas las Redes'),
                      ),
                      ...['AdMob', 'Unity', 'Meta'].map((network) {
                        return DropdownMenuItem(
                          value: network,
                          child: Text(network),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      ref.read(selectedNetworkProvider.notifier).state = value;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: selectedCountry,
                    decoration: const InputDecoration(
                      labelText: 'País',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('Todos los Países'),
                      ),
                      ...['US', 'MX', 'BR', 'GB', 'FR', 'DE'].map((country) {
                        return DropdownMenuItem(
                          value: country,
                          child: Text(country),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      ref.read(selectedCountryProvider.notifier).state = value;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                // Verificar si es el día actual para cambiar el estilo del botón
                _isToday(dateRange)
                    ? ElevatedButton.icon(
                        icon: const Icon(Icons.calendar_today),
                        label: _buildDateRangeLabel(dateRange),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue[50],
                          foregroundColor: Colors.blue[800],
                        ),
                        onPressed: () async {
                          final picked = await showDateRangePicker(
                            context: context,
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                            initialDateRange: DateTimeRange(
                              start: dateRange.startDate,
                              end: dateRange.endDate,
                            ),
                          );
                          if (picked != null) {
                            ref.read(dateRangeProvider.notifier).state = (
                              startDate: picked.start,
                              endDate: picked.end,
                            );
                          }
                        },
                      )
                    : Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          OutlinedButton.icon(
                            icon: const Icon(Icons.calendar_today),
                            label: _buildDateRangeLabel(dateRange),
                            onPressed: () async {
                              final picked = await showDateRangePicker(
                                context: context,
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now(),
                                initialDateRange: DateTimeRange(
                                  start: dateRange.startDate,
                                  end: dateRange.endDate,
                                ),
                              );
                              if (picked != null) {
                                ref.read(dateRangeProvider.notifier).state = (
                                  startDate: picked.start,
                                  endDate: picked.end,
                                );
                              }
                            },
                          ),
                          // Botón para restablecer al día actual
                          if (!_isToday(dateRange))
                            IconButton(
                              icon: const Icon(Icons.today),
                              tooltip: 'Ver solo hoy',
                              onPressed: () {
                                final now = DateTime.now();
                                final today =
                                    DateTime(now.year, now.month, now.day);
                                ref.read(dateRangeProvider.notifier).state = (
                                  startDate: today,
                                  endDate: now,
                                );
                              },
                            ),
                        ],
                      ),
              ],
            ),
          ),
          Expanded(
            child: analyticsData.when(
              data: (data) => SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Dashboard de Administración',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Resumen Global',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 16),
                      GridView.count(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        crossAxisCount: 4,
                        childAspectRatio: 1.5,
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        children: [
                          StatsCard(
                            title: 'Impresiones Totales',
                            value: numberFormat.format(data.totalImpressions),
                            icon: Icons.visibility,
                            iconColor: Colors.blue,
                          ),
                          StatsCard(
                            title: 'Tasa de Finalización',
                            value:
                                '${(data.completionRate * 100).toStringAsFixed(1)}%',
                            icon: Icons.check_circle,
                            iconColor: Colors.green,
                          ),
                          StatsCard(
                            title: 'Ingresos Totales',
                            value: currencyFormat.format(data.totalRevenue),
                            icon: Icons.attach_money,
                            iconColor: Colors.amber,
                          ),
                          StatsCard(
                            title: 'eCPM Promedio',
                            value: currencyFormat.format(data.averageEcpm),
                            icon: Icons.trending_up,
                            iconColor: Colors.purple,
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        'Tendencias',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 300,
                        child: Row(
                          children: [
                            Expanded(
                              child: ImpressionsChart(data: data),
                            ),
                            Expanded(
                              child: RevenueChart(analyticsData: data),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        'Distribución por Red',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 200,
                        child: Row(
                          children: [
                            ...data.networkStats.entries.map((entry) {
                              return Expanded(
                                child: Card(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          entry.key,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                            'Impresiones: ${numberFormat.format(entry.value.impressions)}'),
                                        const SizedBox(height: 4),
                                        Text(
                                            'Ingresos: ${currencyFormat.format(entry.value.revenue)}'),
                                        const SizedBox(height: 4),
                                        Text(
                                            'eCPM: ${currencyFormat.format(entry.value.ecpm)}'),
                                        const SizedBox(height: 8),
                                        LinearProgressIndicator(
                                          value: entry.value.impressions /
                                              data.totalImpressions,
                                          backgroundColor: Colors.grey[200],
                                          color: Colors.blue,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        'Rendimiento por País',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 16),
                      GridView.count(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        crossAxisCount: 3,
                        childAspectRatio: 1.5,
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        children: data.countryStats.entries.map((entry) {
                          return Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        entry.key,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      const Spacer(),
                                      Text(
                                        currencyFormat
                                            .format(entry.value.revenue),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.green,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                      'Impresiones: ${numberFormat.format(entry.value.impressions)}'),
                                  const SizedBox(height: 4),
                                  Text(
                                      'eCPM: ${currencyFormat.format(entry.value.ecpm)}'),
                                  const Spacer(),
                                  LinearProgressIndicator(
                                    value:
                                        entry.value.revenue / data.totalRevenue,
                                    backgroundColor: Colors.grey[200],
                                    color: Colors.green,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              ),
              loading: () => LoadingIndicator(
                message:
                    'Cargando datos (${loadingProgress.current}/${loadingProgress.total})',
                progress: loadingProgress.total > 0
                    ? loadingProgress.current / loadingProgress.total
                    : null,
              ),
              error: (error, stack) => ErrorDisplay(
                message: 'Error al cargar datos: $error',
                onRetry: () => ref.invalidate(analyticsDataProvider),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => ref.invalidate(analyticsDataProvider),
        tooltip: 'Refrescar datos',
        heroTag: 'adminDashboardRefresh',
        child: const Icon(Icons.refresh),
      ),
    );
  }
}
