import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../providers/ecpm_provider.dart';
import '../../models/ecpm_config.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_display.dart';

class ECPMConfigScreen extends ConsumerStatefulWidget {
  const ECPMConfigScreen({super.key});

  @override
  ConsumerState<ECPMConfigScreen> createState() => _ECPMConfigScreenState();
}

class _ECPMConfigScreenState extends ConsumerState<ECPMConfigScreen> {
  String _selectedNetwork = 'AdMob';
  String _selectedAdType = 'reward';
  bool _isEditing = false;
  final Map<String, TextEditingController> _countryControllers = {};
  final TextEditingController _defaultRateController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _defaultRateController.dispose();
    for (var controller in _countryControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final configAsync = ref.watch(ecpmConfigProvider(
        (adNetwork: _selectedNetwork, adType: _selectedAdType)));
    final availableCountries = ref.watch(availableCountriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuración de eCPM'),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.cancel : Icons.edit),
            tooltip: _isEditing ? 'Cancelar' : 'Editar',
            onPressed: () {
              setState(() {
                _isEditing = !_isEditing;
                if (!_isEditing) {
                  // Limpiar controladores al cancelar
                  _countryControllers.clear();
                  _defaultRateController.clear();
                }
              });
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Selector de red y tipo de anuncio
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedNetwork,
                    decoration: const InputDecoration(
                      labelText: 'Red Publicitaria',
                      border: OutlineInputBorder(),
                    ),
                    items: ['AdMob', 'Unity', 'Meta'].map((network) {
                      return DropdownMenuItem(
                        value: network,
                        child: Text(network),
                      );
                    }).toList(),
                    onChanged: _isEditing
                        ? null
                        : (value) {
                            if (value != null) {
                              setState(() {
                                _selectedNetwork = value;
                                _countryControllers.clear();
                                _defaultRateController.clear();
                              });
                            }
                          },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedAdType,
                    decoration: const InputDecoration(
                      labelText: 'Tipo de Anuncio',
                      border: OutlineInputBorder(),
                    ),
                    items: ['reward'].map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type == 'reward' ? 'Recompensa' : type),
                      );
                    }).toList(),
                    onChanged: _isEditing
                        ? null
                        : (value) {
                            if (value != null) {
                              setState(() {
                                _selectedAdType = value;
                                _countryControllers.clear();
                                _defaultRateController.clear();
                              });
                            }
                          },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Contenido principal
            Expanded(
              child: configAsync.when(
                data: (config) {
                  // Inicializar controladores si estamos en modo edición
                  if (_isEditing && _countryControllers.isEmpty) {
                    _initializeControllers(config);
                  }

                  return _isEditing
                      ? _buildEditForm(config, availableCountries)
                      : _buildViewTable(config, availableCountries);
                },
                loading: () => const LoadingIndicator(
                    message: 'Cargando configuración...'),
                error: (error, stack) => ErrorDisplay(
                  message: 'Error al cargar configuración: $error',
                  onRetry: () => ref.invalidate(ecpmConfigProvider),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: _isEditing
          ? FloatingActionButton(
              onPressed: _saveChanges,
              tooltip: 'Guardar cambios',
              heroTag: 'ecpmConfigSave',
              child: const Icon(Icons.save),
            )
          : null,
    );
  }

  void _initializeControllers(ECPMConfig config) {
    _defaultRateController.text = config.defaultRate.toString();

    for (var entry in config.countryRates.entries) {
      _countryControllers[entry.key] =
          TextEditingController(text: entry.value.toString());
    }
  }

  Widget _buildViewTable(ECPMConfig config, List<String> availableCountries) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Información general
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Configuración para ${config.adNetwork} - ${config.adType}',
                    style: const TextStyle(
                        fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                      'Última actualización: ${dateFormat.format(config.lastUpdate)}'),
                  const SizedBox(height: 8),
                  Text(
                      'Tarifa predeterminada: \$${config.defaultRate.toStringAsFixed(2)}'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Tabla de tarifas por país
          const Text(
            'Tarifas por País (eCPM)',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),

          DataTable(
            columns: const [
              DataColumn(label: Text('País')),
              DataColumn(label: Text('eCPM (USD)')),
            ],
            rows: [
              ...config.countryRates.entries.map((entry) {
                return DataRow(
                  cells: [
                    DataCell(Text(entry.key)),
                    DataCell(Text('\$${entry.value.toStringAsFixed(2)}')),
                  ],
                );
              }).toList(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditForm(ECPMConfig config, List<String> availableCountries) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tarifa predeterminada
            TextFormField(
              controller: _defaultRateController,
              decoration: const InputDecoration(
                labelText: 'Tarifa Predeterminada (USD)',
                border: OutlineInputBorder(),
                prefixText: '\$',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Por favor ingresa una tarifa predeterminada';
                }
                if (double.tryParse(value) == null) {
                  return 'Por favor ingresa un número válido';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Tarifas por país
            const Text(
              'Tarifas por País (eCPM)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Lista de países con sus tarifas
            ...availableCountries.map((country) {
              // Crear controlador si no existe
              _countryControllers[country] ??= TextEditingController(
                  text: config.countryRates[country]?.toString() ?? '');

              return Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text(country,
                          style: const TextStyle(fontWeight: FontWeight.bold)),
                    ),
                    Expanded(
                      child: TextFormField(
                        controller: _countryControllers[country],
                        decoration: InputDecoration(
                          labelText: 'eCPM para $country',
                          border: const OutlineInputBorder(),
                          prefixText: '\$',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            if (double.tryParse(value) == null) {
                              return 'Número inválido';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  void _saveChanges() async {
    if (_formKey.currentState!.validate()) {
      // Obtener la configuración actual
      final config = await ref.read(ecpmConfigProvider(
          (adNetwork: _selectedNetwork, adType: _selectedAdType)).future);

      // Actualizar con los nuevos valores
      final defaultRate = double.parse(_defaultRateController.text);
      final Map<String, double> countryRates = {};

      for (var entry in _countryControllers.entries) {
        final value = entry.value.text;
        if (value.isNotEmpty) {
          countryRates[entry.key] = double.parse(value);
        }
      }

      // Crear nueva configuración
      final updatedConfig = config.copyWith(
        defaultRate: defaultRate,
        countryRates: countryRates,
        lastUpdate: DateTime.now(),
      );

      // Guardar cambios
      final result =
          await ref.read(updateEcpmConfigProvider(updatedConfig).future);

      if (mounted) {
        if (result) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Configuración actualizada correctamente')),
          );
          setState(() {
            _isEditing = false;
            _countryControllers.clear();
            _defaultRateController.clear();
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Error al actualizar la configuración'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
