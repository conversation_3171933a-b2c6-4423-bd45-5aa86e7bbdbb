import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math' as math;
import '../../models/user_management.dart';
import '../../models/user_data.dart';
import '../../models/withdrawal_management.dart';
import '../../services/admin_service.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_display.dart';
import '../../widgets/common/index_error_display.dart';

// Providers para la gestión de usuarios
final usersProvider =
    StateNotifierProvider<UsersNotifier, AsyncValue<List<UserManagement>>>(
  (ref) => UsersNotifier(),
);

class UsersNotifier extends StateNotifier<AsyncValue<List<UserManagement>>> {
  UsersNotifier() : super(const AsyncValue.loading()) {
    loadUsers();
  }

  String? _selectedCountry;
  bool? _selectedActiveStatus;
  DocumentSnapshot? _lastDocument;
  bool _hasMoreUsers = true;

  void setFilters(String? country, bool? isActive) {
    print(
        'UsersNotifier: Aplicando filtros - country: $country, isActive: $isActive');
    _selectedCountry = country;
    _selectedActiveStatus = isActive;
    _lastDocument = null;
    _hasMoreUsers = true;
    loadUsers();
  }

  Future<void> loadUsers() async {
    try {
      print('UsersNotifier: Cargando usuarios iniciales');
      state = const AsyncValue.loading();

      final users = await AdminService.getUsers(
        country: _selectedCountry,
        isActive: _selectedActiveStatus,
        limit: 20,
        startAfter: null,
      );

      print('UsersNotifier: ${users.length} usuarios cargados');

      if (users.isNotEmpty) {
        // Obtener el último documento para paginación
        final doc = await FirebaseFirestore.instance
            .collection('projects/pinataparty/users')
            .doc(users.last.id)
            .get();
        _lastDocument = doc;
        print('UsersNotifier: Último documento actualizado: ${users.last.id}');
      }

      if (users.length < 20) {
        _hasMoreUsers = false;
        print('UsersNotifier: No hay más usuarios para cargar');
      } else {
        _hasMoreUsers = true;
        print('UsersNotifier: Hay más usuarios disponibles para cargar');
      }

      state = AsyncValue.data(users);
      print('UsersNotifier: Estado actualizado con ${users.length} usuarios');
    } catch (e, stack) {
      print('UsersNotifier: Error al cargar usuarios: $e');
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> loadMoreUsers() async {
    if (!_hasMoreUsers || _lastDocument == null) {
      print(
          'UsersNotifier: No se pueden cargar más usuarios: hasMoreUsers=$_hasMoreUsers, lastDocument=${_lastDocument != null}');
      return;
    }

    try {
      print('UsersNotifier: Cargando más usuarios');
      final currentUsers = state.value ?? [];
      print('UsersNotifier: Usuarios actuales: ${currentUsers.length}');

      final moreUsers = await AdminService.getUsers(
        country: _selectedCountry,
        isActive: _selectedActiveStatus,
        limit: 20,
        startAfter: _lastDocument,
      );

      print('UsersNotifier: ${moreUsers.length} usuarios adicionales cargados');

      if (moreUsers.isEmpty) {
        _hasMoreUsers = false;
        print('UsersNotifier: No hay más usuarios para cargar (lista vacía)');
        return;
      }

      // Actualizar el último documento para la siguiente página
      final doc = await FirebaseFirestore.instance
          .collection('projects/pinataparty/users')
          .doc(moreUsers.last.id)
          .get();
      _lastDocument = doc;
      print(
          'UsersNotifier: Último documento actualizado: ${moreUsers.last.id}');

      if (moreUsers.length < 20) {
        _hasMoreUsers = false;
        print('UsersNotifier: No hay más usuarios para cargar (menos de 20)');
      } else {
        print('UsersNotifier: Hay más usuarios disponibles para cargar');
      }

      // Actualizar la lista con los nuevos usuarios
      final updatedUsers = [...currentUsers, ...moreUsers];
      state = AsyncValue.data(updatedUsers);
      print(
          'UsersNotifier: Estado actualizado con ${updatedUsers.length} usuarios totales');
    } catch (e) {
      // No actualizamos el estado en caso de error para mantener los datos existentes
      print('UsersNotifier: Error al cargar más usuarios: $e');
    }
  }

  bool get canLoadMore => _hasMoreUsers;
}

final userDetailProvider =
    FutureProvider.family<UserProfile, String>((ref, userId) async {
  return await AdminService.getUserDetail(userId);
});

final userWithdrawalsProvider =
    FutureProvider.family<List<WithdrawalManagement>, String>(
        (ref, userId) async {
  return await AdminService.getUserWithdrawals(userId);
});

class UserManagementScreen extends ConsumerStatefulWidget {
  const UserManagementScreen({super.key});

  @override
  ConsumerState<UserManagementScreen> createState() =>
      _UserManagementScreenState();
}

class _UserManagementScreenState extends ConsumerState<UserManagementScreen> {
  String? _selectedCountry;
  bool? _selectedActiveStatus;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      final usersNotifier = ref.read(usersProvider.notifier);
      if (usersNotifier.canLoadMore) {
        usersNotifier.loadMoreUsers();
      }
    }
  }

  void _refreshData() {
    final usersNotifier = ref.read(usersProvider.notifier);
    usersNotifier.loadUsers();
  }

  void _applyFilters() {
    final usersNotifier = ref.read(usersProvider.notifier);
    usersNotifier.setFilters(_selectedCountry, _selectedActiveStatus);
  }

  Future<void> _toggleUserStatus(String userId, bool currentStatus) async {
    try {
      await AdminService.toggleUserStatus(userId, !currentStatus);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(!currentStatus
                ? 'Usuario activado correctamente'
                : 'Usuario desactivado correctamente'),
          ),
        );
        // Actualizar la lista de usuarios
        _refreshData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${e.toString()}')),
        );
      }
    }
  }

  void _showUserDetail(String userId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserDetailScreen(userId: userId),
      ),
    ).then((_) {
      // Actualizar la lista de usuarios cuando regrese de la pantalla de detalle
      _refreshData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestión de Usuarios'),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: _selectedCountry,
                    decoration: const InputDecoration(
                      labelText: 'País',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('Todos los países'),
                      ),
                      ...['MX', 'US', 'BR', 'GB', 'FR', 'DE'].map((country) {
                        return DropdownMenuItem(
                          value: country,
                          child: Text(country),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCountry = value;
                      });
                      _applyFilters();
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<bool?>(
                    value: _selectedActiveStatus,
                    decoration: const InputDecoration(
                      labelText: 'Estado',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: null,
                        child: Text('Todos'),
                      ),
                      DropdownMenuItem(
                        value: true,
                        child: Text('Activos'),
                      ),
                      DropdownMenuItem(
                        value: false,
                        child: Text('Inactivos'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedActiveStatus = value;
                      });
                      _applyFilters();
                    },
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Consumer(
              builder: (context, ref, child) {
                final usersAsync = ref.watch(usersProvider);
                final usersNotifier = ref.watch(usersProvider.notifier);

                return usersAsync.when(
                  data: (users) {
                    if (users.isEmpty) {
                      return const Center(
                        child: Text('No hay usuarios con estos filtros'),
                      );
                    }

                    return ListView.builder(
                      controller: _scrollController,
                      itemCount:
                          users.length + (usersNotifier.canLoadMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index == users.length) {
                          return const Padding(
                            padding: EdgeInsets.symmetric(vertical: 16.0),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        }

                        final user = users[index];
                        return _buildUserCard(user);
                      },
                    );
                  },
                  loading: () =>
                      const LoadingIndicator(message: 'Cargando usuarios...'),
                  error: (error, stack) {
                    // Verificar si es un error de índice
                    if (error.toString().contains('index')) {
                      return IndexErrorDisplay(
                        message:
                            'Se requiere un índice compuesto para cargar los usuarios.',
                        indexDefinition: '''
{
  "collectionGroup": "users",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "country", "order": "ASCENDING" },
    { "fieldPath": "isActive", "order": "ASCENDING" },
    { "fieldPath": "createAt", "order": "DESCENDING" }
  ]
}
''',
                        onRetry: _refreshData,
                      );
                    }

                    // Otros errores
                    return ErrorDisplay(
                      message: 'Error al cargar usuarios: $error',
                      onRetry: _refreshData,
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _refreshData,
        tooltip: 'Refrescar',
        heroTag: 'userManagementRefresh',
        child: const Icon(Icons.refresh),
      ),
    );
  }

  Widget _buildUserCard(UserManagement user) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    // Formato de moneda con 4 decimales para mostrar valores pequeños como 0.002
    final currencyFormat =
        NumberFormat.currency(symbol: '\$', decimalDigits: 4);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => _showUserDetail(user.id),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      user.email,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: user.isActive
                          ? Colors.green.withOpacity(0.2)
                          : Colors.red.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          user.isActive ? Icons.check_circle : Icons.cancel,
                          size: 16,
                          color: user.isActive ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          user.isActive ? 'ACTIVO' : 'INACTIVO',
                          style: TextStyle(
                            color: user.isActive ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const Divider(),
              // Información básica
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('País'),
                        Text(
                          user.country,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Fecha de Registro'),
                        Text(
                          user.createAt != null
                              ? dateFormat.format(user.createAt!)
                              : 'No disponible',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Email Verificado'),
                        Text(
                          user.isEmailVerified ? 'Sí' : 'No',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Métricas de anuncios
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Anuncios Completados'),
                        Text(
                          '${user.completedRewardAds}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Anuncios Procesados'),
                        Text(
                          '${user.processedRewardAds}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Ganancias Disponibles'),
                        Text(
                          currencyFormat.format(user.availableAdEarnings),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Información financiera
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Ganancias Totales'),
                        Text(
                          currencyFormat.format(user.adEarnings),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Código de Referido'),
                        Text(
                          user.referralCode ?? 'No asignado',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Número de Referidos'),
                        Text(
                          '${user.referralCount}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton.icon(
                    onPressed: () => _toggleUserStatus(user.id, user.isActive),
                    icon: Icon(
                      user.isActive ? Icons.block : Icons.check_circle,
                      color: user.isActive ? Colors.red : Colors.green,
                    ),
                    label: Text(user.isActive ? 'Desactivar' : 'Activar'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor:
                          user.isActive ? Colors.red : Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: () => _showUserDetail(user.id),
                    icon: const Icon(Icons.visibility),
                    label: const Text('Ver Detalle'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class UserDetailScreen extends ConsumerWidget {
  final String userId;

  const UserDetailScreen({
    super.key,
    required this.userId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userDetail = ref.watch(userDetailProvider(userId));
    final userWithdrawals = ref.watch(userWithdrawalsProvider(userId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Detalle de Usuario'),
      ),
      body: userDetail.when(
        data: (user) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildUserInfoCard(user),
                const SizedBox(height: 24),
                const Text(
                  'Historial de Retiros',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                userWithdrawals.when(
                  data: (withdrawals) {
                    if (withdrawals.isEmpty) {
                      return const Card(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                            child: Text('Este usuario no ha realizado retiros'),
                          ),
                        ),
                      );
                    }

                    return ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: withdrawals.length,
                      itemBuilder: (context, index) {
                        return _buildWithdrawalHistoryCard(withdrawals[index]);
                      },
                    );
                  },
                  loading: () => const LoadingIndicator(
                      message: 'Cargando historial de retiros...'),
                  error: (error, stack) {
                    // Verificar si es un error de índice
                    if (error.toString().contains('index')) {
                      return IndexErrorDisplay(
                        message:
                            'Se requiere un índice compuesto para cargar el historial de retiros del usuario.',
                        indexDefinition: '''
{
  "collectionGroup": "withdrawals",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "userId", "order": "ASCENDING" },
    { "fieldPath": "requestDate", "order": "DESCENDING" }
  ]
}
''',
                        onRetry: () =>
                            ref.invalidate(userWithdrawalsProvider(userId)),
                      );
                    }

                    // Otros errores
                    return ErrorDisplay(
                      message: 'Error al cargar historial de retiros: $error',
                      onRetry: () =>
                          ref.invalidate(userWithdrawalsProvider(userId)),
                    );
                  },
                ),
              ],
            ),
          );
        },
        loading: () =>
            const LoadingIndicator(message: 'Cargando detalle de usuario...'),
        error: (error, stack) => ErrorDisplay(
          message: 'Error al cargar detalle de usuario: $error',
          onRetry: () => ref.invalidate(userDetailProvider(userId)),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard(UserProfile user) {
    final timeFormat = DateFormat('dd/MM/yyyy HH:mm');
    // Formato de moneda con 4 decimales para mostrar valores pequeños como 0.002
    final currencyFormat =
        NumberFormat.currency(symbol: '\$', decimalDigits: 4);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.blue,
                  child: Icon(Icons.person, size: 40, color: Colors.white),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.email,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'ID: ${user.id}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: user.isActive
                        ? Colors.green.withOpacity(0.2)
                        : Colors.red.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        user.isActive ? Icons.check_circle : Icons.cancel,
                        size: 16,
                        color: user.isActive ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        user.isActive ? 'ACTIVO' : 'INACTIVO',
                        style: TextStyle(
                          color: user.isActive ? Colors.green : Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(height: 32),
            // Información básica
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('País', user.country),
                ),
                Expanded(
                  child: _buildInfoItem(
                      'Teléfono', user.phoneNumber ?? 'No registrado'),
                ),
                Expanded(
                  child: _buildInfoItem(
                      'Fecha de Registro', timeFormat.format(user.createAt)),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Verificación
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                      'Email Verificado', user.isEmailVerified ? 'Sí' : 'No'),
                ),
                Expanded(
                  child: _buildInfoItem('Teléfono Verificado',
                      user.isPhoneVerified ? 'Sí' : 'No'),
                ),
                Expanded(
                  child:
                      _buildInfoItem('Última Actualización', 'No disponible'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Métricas de anuncios
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                      'Anuncios Completados', '${user.completedRewardAds}'),
                ),
                Expanded(
                  child: _buildInfoItem(
                      'Anuncios Procesados', '${user.processedRewardAds}'),
                ),
                Expanded(
                  child: _buildInfoItem(
                      'Último Inicio de Sesión', 'No disponible'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Información financiera
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Ganancias Totales (Acumuladas)',
                      currencyFormat.format(user.adEarnings)),
                ),
                Expanded(
                  child: _buildInfoItem('Ganancias Disponibles (Para Retirar)',
                      currencyFormat.format(user.availableAdEarnings)),
                ),
                Expanded(
                  child: _buildInfoItem('Recompensa por Referidos',
                      currencyFormat.format(user.totalReward)),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Ganancias estimadas y fecha de distribución
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Ganancias Estimadas (No Confirmadas)',
                      currencyFormat.format(user.estimatedEarnings)),
                ),
                Expanded(
                  child: _buildInfoItem(
                      'Última Distribución',
                      user.lastDistributionDate != null
                          ? timeFormat.format(user.lastDistributionDate!)
                          : 'No disponible'),
                ),
                Expanded(
                  child: _buildInfoItem(
                      'Referido por', user.referredBy ?? 'No referido'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Sistema de referidos
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                      'Código de Referido', user.referralCode ?? 'No asignado'),
                ),
                Expanded(
                  child: _buildInfoItem(
                      'Número de Referidos', '${user.referralCount}'),
                ),
                const Expanded(
                    child:
                        SizedBox()), // Espacio vacío para mantener la simetría
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  Widget _buildWithdrawalHistoryCard(WithdrawalManagement withdrawal) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    final currencyFormat = NumberFormat.currency(symbol: '\$');

    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (withdrawal.status) {
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = 'PENDIENTE';
        break;
      case 'approved':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'APROBADO';
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        statusText = 'RECHAZADO';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
        statusText = withdrawal.status.toUpperCase();
    }

    // Formatear los detalles del retiro para mejor visualización
    String formattedDetails = '';
    if (withdrawal.details.isNotEmpty) {
      withdrawal.details.forEach((key, value) {
        formattedDetails += '• $key: $value\n';
      });
    }

    String withdrawalType = withdrawal.type == 'mobile_recharge'
        ? 'Recarga Móvil'
        : withdrawal.type == 'gift_card'
            ? 'Tarjeta de Regalo'
            : withdrawal.type;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Retiro #${withdrawal.id.substring(0, math.min(8, withdrawal.id.length))}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Monto Solicitado'),
                      Text(
                        currencyFormat.format(withdrawal.amount),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Tipo de Retiro'),
                      Text(
                        withdrawalType,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Fecha de Solicitud'),
                      Text(
                        dateFormat.format(withdrawal.requestDate),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Detalles del Retiro:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              formattedDetails.isEmpty
                  ? 'Sin detalles adicionales'
                  : formattedDetails,
              style: const TextStyle(fontSize: 14),
            ),
            if (withdrawal.processedDate != null) ...[
              const SizedBox(height: 16),
              const Text(
                'Información de Procesamiento:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Fecha de Procesamiento: ${dateFormat.format(withdrawal.processedDate!)}',
                style: const TextStyle(fontSize: 14),
              ),
              if (withdrawal.processedBy != null)
                Text(
                  'Procesado por: ${withdrawal.processedBy}',
                  style: const TextStyle(fontSize: 14),
                ),
              if (withdrawal.rejectionReason != null)
                Text(
                  'Motivo de rechazo: ${withdrawal.rejectionReason}',
                  style: const TextStyle(fontSize: 14, color: Colors.red),
                ),
            ],
          ],
        ),
      ),
    );
  }
}
