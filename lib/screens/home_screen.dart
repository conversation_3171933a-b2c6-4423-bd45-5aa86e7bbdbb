import 'package:flutter/material.dart';
import '../services/firebase_service.dart';
import '../services/admin_service.dart';
import '../services/user_service.dart';
import 'admin/admin_dashboard_screen.dart';
import 'admin/user_management_screen.dart';
import 'admin/withdrawal_management_screen.dart';
import 'admin/earnings_distribution_screen.dart';
import 'admin/ecpm_config_screen.dart';
import 'user/user_dashboard_screen.dart';
import 'user/user_history_screen.dart';
import 'user/user_withdrawals_screen.dart';
import 'user/offerwall_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  // Método público para cambiar el índice seleccionado
  void navigateTo(int index) {
    setState(() => _selectedIndex = index);
  }

  @override
  void initState() {
    super.initState();
    // Calcular las ganancias del usuario cuando se cargue la página
    _calculateUserEarnings();
  }

  Future<void> _calculateUserEarnings() async {
    if (!FirebaseService.isUserAdmin) {
      try {
        // Calcular las ganancias del usuario
        await UserService.getCurrentUserProfile();
      } catch (e) {
        print('Error al calcular ganancias del usuario: $e');
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // This ensures that when returning to this screen, we properly refresh the state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        // Just trigger a rebuild
      });

      // Recalcular las ganancias del usuario cuando se regrese a esta pantalla
      _calculateUserEarnings();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isAdmin = FirebaseService.isUserAdmin;

    return Scaffold(
      appBar: AppBar(
        title: const Text('PP Analytics'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await FirebaseService.signOut();
              if (mounted) {
                Navigator.pushReplacementNamed(context, '/login');
              }
            },
          ),
        ],
      ),
      drawer: NavigationDrawer(
        selectedIndex: _selectedIndex,
        onDestinationSelected: (index) {
          setState(() => _selectedIndex = index);
          Navigator.pop(context);
        },
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              'Menu',
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          NavigationDrawerDestination(
            icon: const Icon(Icons.dashboard_outlined),
            selectedIcon: const Icon(Icons.dashboard),
            label: const Text('Dashboard'),
          ),
          if (isAdmin) ...[
            NavigationDrawerDestination(
              icon: const Icon(Icons.people_outline),
              selectedIcon: const Icon(Icons.people),
              label: const Text('Usuarios'),
            ),
            NavigationDrawerDestination(
              icon: const Icon(Icons.calculate_outlined),
              selectedIcon: const Icon(Icons.calculate),
              label: const Text('Distribución'),
            ),
            NavigationDrawerDestination(
              icon: const Icon(Icons.attach_money_outlined),
              selectedIcon: const Icon(Icons.attach_money),
              label: const Text('Config. eCPM'),
            ),
            NavigationDrawerDestination(
              icon: const Icon(Icons.pending_actions_outlined),
              selectedIcon: const Icon(Icons.pending_actions),
              label: const Text('Retiros Pendientes'),
            ),
          ] else ...[
            NavigationDrawerDestination(
              icon: const Icon(Icons.card_giftcard_outlined),
              selectedIcon: const Icon(Icons.card_giftcard),
              label: const Text('Ofertas'),
            ),
            NavigationDrawerDestination(
              icon: const Icon(Icons.history_outlined),
              selectedIcon: const Icon(Icons.history),
              label: const Text('Historial'),
            ),
            NavigationDrawerDestination(
              icon: const Icon(Icons.account_balance_wallet_outlined),
              selectedIcon: const Icon(Icons.account_balance_wallet),
              label: const Text('Retiros'),
            ),
          ],
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: [
          isAdmin ? const AdminDashboardScreen() : const UserDashboardScreen(),
          if (isAdmin) ...[
            const UserManagementScreen(),
            const EarningsDistributionScreen(),
            const ECPMConfigScreen(),
            const WithdrawalManagementScreen(),
          ] else ...[
            const OfferwallScreen(),
            const UserHistoryScreen(),
            const UserWithdrawalsScreen(),
          ],
        ],
      ),
    );
  }
}
