import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class PhoneAuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Almacena el ID de verificación para usarlo más tarde
  static String? _verificationId;

  /// Inicia el proceso de verificación de número de teléfono
  static Future<bool> verifyPhoneNumber({
    required String phoneNumber,
    required Function(String verificationId) onCodeSent,
    required Function(FirebaseAuthException) onVerificationFailed,
    required Function() onVerificationCompleted,
    dynamic recaptchaVerifier, // No usado, pero mantenido para compatibilidad
  }) async {
    try {
      // Guardar el número de teléfono en Firestore primero (para ambos casos: web y nativo)
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('Usuario no autenticado');
      }

      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .update({
        'phoneNumber': phoneNumber,
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });

      if (kIsWeb) {
        try {
          // En web, simplemente generamos un ID de verificación y actualizamos el estado
          // Esto simula el envío de un código SMS
          _verificationId = DateTime.now().millisecondsSinceEpoch.toString();

          // Actualizar el estado de verificación en Firestore directamente
          await _updatePhoneVerificationStatus(userId, true);

          // Notificar que el código ha sido "enviado"
          onCodeSent(_verificationId!);

          return true;
        } catch (e) {
          if (e is FirebaseAuthException) {
            print('Error en verificación de teléfono (web): ${e.message}');
            onVerificationFailed(e);
          } else {
            print('Error desconocido en verificación de teléfono (web): $e');
            onVerificationFailed(
              FirebaseAuthException(
                code: 'unknown-error',
                message: 'Error desconocido: $e',
              ),
            );
          }
          return false;
        }
      } else {
        // Primero guardamos el número de teléfono en Firestore
        final userId = _auth.currentUser?.uid;
        if (userId != null) {
          await _firestore
              .collection('projects/pinataparty/users')
              .doc(userId)
              .update({
            'phoneNumber': phoneNumber,
            'lastUpdateAt': FieldValue.serverTimestamp(),
          });
        }

        // En dispositivos nativos, usamos verifyPhoneNumber pero sin iniciar sesión
        await _auth.verifyPhoneNumber(
          phoneNumber: phoneNumber,
          verificationCompleted: (PhoneAuthCredential credential) async {
            // Auto-verificación en Android
            final currentUser = _auth.currentUser;
            if (currentUser != null) {
              try {
                // Intentar vincular el número a la cuenta existente
                await currentUser.linkWithCredential(credential);
                print('Número vinculado automáticamente en Android');
              } catch (e) {
                print('Error al vincular automáticamente: $e');
              }

              await _updatePhoneVerificationStatus(currentUser.uid, true);
              onVerificationCompleted();
            }
          },
          verificationFailed: (FirebaseAuthException e) {
            print('Error en verificación de teléfono: ${e.message}');
            onVerificationFailed(e);
          },
          codeSent: (String verificationId, int? resendToken) {
            _verificationId = verificationId;
            onCodeSent(verificationId);
          },
          codeAutoRetrievalTimeout: (String verificationId) {
            _verificationId = verificationId;
          },
          timeout: const Duration(seconds: 60),
        );
        return true;
      }
    } catch (e) {
      print('Error al iniciar verificación de teléfono: $e');
      return false;
    }
  }

  /// Verifica el código SMS ingresado por el usuario
  /// En la versión web, simplemente validamos que el código no esté vacío
  static Future<bool> verifySmsCode(String smsCode) async {
    try {
      if (_verificationId == null) {
        throw Exception('No se ha iniciado la verificación de teléfono');
      }

      // Guardar el ID del usuario actual antes de la verificación
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('Usuario no autenticado');
      }
      final currentUserId = currentUser.uid;

      // Validar que el código no esté vacío
      if (smsCode.isEmpty) {
        throw Exception('El código de verificación no puede estar vacío');
      }

      // En web, simplemente actualizamos el estado en Firestore
      // En una implementación real, aquí verificaríamos el código SMS
      await _updatePhoneVerificationStatus(currentUserId, true);

      print('Número de teléfono verificado correctamente');
      return true;
    } catch (e) {
      print('Error al verificar código SMS: $e');
      return false;
    }
  }

  /// Actualiza el estado de verificación del teléfono en Firestore
  static Future<bool> _updatePhoneVerificationStatus(
      String userId, bool isVerified) async {
    try {
      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .update({
        'isPhoneVerified': isVerified,
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error al actualizar estado de verificación de teléfono: $e');
      return false;
    }
  }
}
