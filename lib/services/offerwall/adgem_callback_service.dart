import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';

/// Servicio para manejar callbacks de AdGem
/// 
/// Este servicio procesa las notificaciones de completación de ofertas
/// enviadas por AdGem cuando un usuario completa una oferta exitosamente.
class AdGemCallbackService {
  static const String _postbackKey = 'hhha9f9g64h807ackda78f65';
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// Procesa un callback de AdGem
  /// 
  /// [params] contiene todos los parámetros enviados por AdGem
  /// Retorna true si el callback fue procesado exitosamente
  static Future<bool> processCallback(Map<String, String> params) async {
    try {
      print('AdGem Callback recibido: $params');
      
      // Validar parámetros requeridos
      final validation = _validateRequiredParams(params);
      if (!validation.isValid) {
        print('AdGem Callback: Parámetros inválidos - ${validation.error}');
        return false;
      }
      
      // Extraer datos del callback
      final callbackData = _extractCallbackData(params);
      
      // Verificar si la transacción ya fue procesada
      final isDuplicate = await _checkDuplicateTransaction(callbackData.transactionId);
      if (isDuplicate) {
        print('AdGem Callback: Transacción duplicada - ${callbackData.transactionId}');
        return true; // Retornar true para evitar reintentos
      }
      
      // Procesar la completación de la oferta
      await _processOfferCompletion(callbackData);
      
      // Actualizar las ganancias del usuario
      await _updateUserEarnings(callbackData);
      
      // Registrar la transacción
      await _recordTransaction(callbackData);
      
      print('AdGem Callback procesado exitosamente para usuario: ${callbackData.playerId}');
      return true;
      
    } catch (e) {
      print('Error procesando AdGem callback: $e');
      return false;
    }
  }
  
  /// Valida que todos los parámetros requeridos estén presentes
  static ({bool isValid, String? error}) _validateRequiredParams(Map<String, String> params) {
    final required = ['appid', 'playerid', 'offerid', 'amount', 'transactionid'];
    
    for (final param in required) {
      if (!params.containsKey(param) || params[param]?.isEmpty == true) {
        return (isValid: false, error: 'Parámetro requerido faltante: $param');
      }
    }
    
    // Validar que el appid sea correcto
    if (params['appid'] != '30519') {
      return (isValid: false, error: 'App ID incorrecto: ${params['appid']}');
    }
    
    return (isValid: true, error: null);
  }
  
  /// Extrae y estructura los datos del callback
  static AdGemCallbackData _extractCallbackData(Map<String, String> params) {
    return AdGemCallbackData(
      appId: params['appid']!,
      playerId: params['playerid']!,
      offerId: params['offerid']!,
      amount: double.tryParse(params['amount'] ?? '0') ?? 0.0,
      transactionId: params['transactionid']!,
      payout: double.tryParse(params['payout'] ?? '0') ?? 0.0,
      country: params['country'],
      ip: params['ip'],
      conversionDateTime: params['datetime'],
      c1: params['c1'],
      c2: params['c2'],
    );
  }
  
  /// Verifica si una transacción ya fue procesada
  static Future<bool> _checkDuplicateTransaction(String transactionId) async {
    final doc = await _firestore
        .collection('projects/pinataparty/offerwall/completions')
        .where('transactionId', isEqualTo: transactionId)
        .limit(1)
        .get();
    
    return doc.docs.isNotEmpty;
  }
  
  /// Procesa la completación de la oferta
  static Future<void> _processOfferCompletion(AdGemCallbackData data) async {
    final completionData = {
      'userId': data.playerId,
      'offerId': data.offerId,
      'provider': 'AdGem',
      'startedAt': FieldValue.serverTimestamp(),
      'completedAt': FieldValue.serverTimestamp(),
      'verifiedAt': FieldValue.serverTimestamp(),
      'status': 'verified',
      'rewardAmount': data.payout > 0 ? data.payout : data.amount / 100, // Convertir puntos a USD si es necesario
      'currency': 'USD',
      'transactionId': data.transactionId,
      'providerData': {
        'appId': data.appId,
        'amount': data.amount,
        'payout': data.payout,
        'country': data.country,
        'ip': data.ip,
        'conversionDateTime': data.conversionDateTime,
        'c1': data.c1,
        'c2': data.c2,
      },
      'retryCount': 0,
      'createdAt': FieldValue.serverTimestamp(),
    };
    
    await _firestore
        .collection('projects/pinataparty/offerwall/completions')
        .doc(data.transactionId)
        .set(completionData);
  }
  
  /// Actualiza las ganancias del usuario
  static Future<void> _updateUserEarnings(AdGemCallbackData data) async {
    final userId = data.playerId;
    final rewardAmount = data.payout > 0 ? data.payout : data.amount / 100;
    
    // Obtener o crear el documento de ganancias del usuario
    final earningsRef = _firestore
        .collection('projects/pinataparty/offerwall/earnings')
        .doc(userId);
    
    await _firestore.runTransaction((transaction) async {
      final earningsDoc = await transaction.get(earningsRef);
      
      if (earningsDoc.exists) {
        // Actualizar ganancias existentes
        final currentData = earningsDoc.data()!;
        transaction.update(earningsRef, {
          'totalEarnings': (currentData['totalEarnings'] ?? 0.0) + rewardAmount,
          'availableEarnings': (currentData['availableEarnings'] ?? 0.0) + rewardAmount,
          'completedOffers': (currentData['completedOffers'] ?? 0) + 1,
          'lastOfferDate': FieldValue.serverTimestamp(),
          'earningsByProvider.AdGem': (currentData['earningsByProvider']?['AdGem'] ?? 0.0) + rewardAmount,
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      } else {
        // Crear nuevo documento de ganancias
        transaction.set(earningsRef, {
          'totalEarnings': rewardAmount,
          'availableEarnings': rewardAmount,
          'pendingEarnings': 0.0,
          'completedOffers': 1,
          'pendingOffers': 0,
          'rejectedOffers': 0,
          'lastOfferDate': FieldValue.serverTimestamp(),
          'earningsByProvider': {'AdGem': rewardAmount},
          'offersByCategory': {},
          'averageRewardPerOffer': rewardAmount,
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      }
    });
    
    // También actualizar el perfil principal del usuario si es necesario
    await _updateUserProfile(userId, rewardAmount);
  }
  
  /// Actualiza el perfil principal del usuario con las ganancias de offerwall
  static Future<void> _updateUserProfile(String userId, double rewardAmount) async {
    final userRef = _firestore
        .collection('projects/pinataparty/users')
        .doc(userId);
    
    await _firestore.runTransaction((transaction) async {
      final userDoc = await transaction.get(userRef);
      
      if (userDoc.exists) {
        final currentData = userDoc.data()!;
        transaction.update(userRef, {
          'offerwallEarnings': (currentData['offerwallEarnings'] ?? 0.0) + rewardAmount,
          'lastOfferwallEarning': rewardAmount,
          'lastOfferwallDate': FieldValue.serverTimestamp(),
          'lastUpdateAt': FieldValue.serverTimestamp(),
        });
      }
    });
  }
  
  /// Registra la transacción para auditoría
  static Future<void> _recordTransaction(AdGemCallbackData data) async {
    final transactionData = {
      'transactionId': data.transactionId,
      'provider': 'AdGem',
      'userId': data.playerId,
      'offerId': data.offerId,
      'amount': data.amount,
      'payout': data.payout,
      'country': data.country,
      'ip': data.ip,
      'conversionDateTime': data.conversionDateTime,
      'processedAt': FieldValue.serverTimestamp(),
      'status': 'completed',
    };
    
    await _firestore
        .collection('projects/pinataparty/offerwall/transactions')
        .doc(data.transactionId)
        .set(transactionData);
  }
}

/// Clase para estructurar los datos del callback de AdGem
class AdGemCallbackData {
  final String appId;
  final String playerId;
  final String offerId;
  final double amount;
  final String transactionId;
  final double payout;
  final String? country;
  final String? ip;
  final String? conversionDateTime;
  final String? c1;
  final String? c2;
  
  AdGemCallbackData({
    required this.appId,
    required this.playerId,
    required this.offerId,
    required this.amount,
    required this.transactionId,
    required this.payout,
    this.country,
    this.ip,
    this.conversionDateTime,
    this.c1,
    this.c2,
  });
}
