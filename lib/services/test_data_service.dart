import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/models.dart';

/// Servicio para inicializar datos de prueba para todas las colecciones
///
/// Esta clase centraliza la generación de datos de prueba para todas las colecciones
/// que se utilizan en el sistema, asegurando coherencia entre ellas.
class TestDataService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final Random _random = Random();

  /// Inicializa todos los datos de prueba para el sistema
  ///
  /// Este método crea datos de prueba para todas las colecciones:
  /// - users_v1: Usuarios del sistema web (SOLO PARA PRUEBAS, en producción se usa users)
  /// - withdrawals: Solicitudes de retiro
  /// - adAnalytics/impressions/records: Impresiones de anuncios
  /// - adAnalytics/config/eCPM: Configuración de eCPM
  /// - adminSettings/withdrawalSettings: Configuración de retiros
  /// - adminLogs: Logs de acciones administrativas
  static Future<void> initializeAllTestData({
    Function(String message, double progress)? onProgress,
  }) async {
    try {
      onProgress?.call('Iniciando creación de datos de prueba...', 0.0);

      // 1. Crear configuración de retiros
      onProgress?.call('Creando configuración de retiros...', 0.1);
      await _initializeWithdrawalSettings();

      // 2. Crear configuración de eCPM
      onProgress?.call('Creando configuración de eCPM...', 0.2);
      await _initializeEcpmConfig();

      // 3. Crear usuarios de prueba
      onProgress?.call('Creando usuarios de prueba...', 0.3);
      final userIds = await _initializeUsers();

      // 4. Generar impresiones de anuncios
      onProgress?.call('Generando impresiones de anuncios...', 0.4);
      await _initializeAdImpressions(userIds);

      // 5. Crear retiros de prueba
      onProgress?.call('Creando retiros de prueba...', 0.7);
      await _initializeWithdrawals(userIds);

      // 6. Crear logs administrativos
      onProgress?.call('Creando logs administrativos...', 0.9);
      await _initializeAdminLogs();

      onProgress?.call('Datos de prueba inicializados correctamente', 1.0);
      print('Todos los datos de prueba han sido inicializados correctamente');
    } catch (e) {
      print('Error al inicializar datos de prueba: $e');
      throw Exception('Error al inicializar datos de prueba: $e');
    }
  }

  /// Inicializa la configuración de retiros
  static Future<WithdrawalSettings> _initializeWithdrawalSettings() async {
    try {
      final withdrawalSettings = WithdrawalSettings.defaultSettings();
      await _firestore
          .doc(WithdrawalSettings.documentPath())
          .set(withdrawalSettings.toFirestore());
      print('Configuración de retiros inicializada correctamente');
      return withdrawalSettings;
    } catch (e) {
      print('Error al inicializar configuración de retiros: $e');
      throw e;
    }
  }

  /// Inicializa la configuración de eCPM para diferentes redes y países
  static Future<void> _initializeEcpmConfig() async {
    try {
      final adNetworks = ['AdMob', 'Unity', 'Meta'];
      final adTypes = ['reward']; // Solo usamos reward por ahora

      for (final network in adNetworks) {
        for (final adType in adTypes) {
          final config = ECPMConfig(
            adNetwork: network,
            adType: adType,
            countryRates: {
              'US': 10.0 + _random.nextDouble() * 2.0,
              'GB': 8.0 + _random.nextDouble() * 2.0,
              'DE': 7.0 + _random.nextDouble() * 1.5,
              'FR': 6.0 + _random.nextDouble() * 1.5,
              'JP': 9.0 + _random.nextDouble() * 2.0,
              'KR': 7.0 + _random.nextDouble() * 1.5,
              'AU': 7.5 + _random.nextDouble() * 1.5,
              'CA': 8.0 + _random.nextDouble() * 1.5,
              'MX': 4.0 + _random.nextDouble() * 1.0,
              'BR': 3.5 + _random.nextDouble() * 1.0,
            },
            defaultRate: 2.0 + _random.nextDouble() * 0.5,
            lastUpdate: DateTime.now(),
          );

          await _firestore
              .collection('projects/pinataparty/adAnalytics/config/eCPM')
              .doc('${config.adNetwork}-${config.adType}')
              .set(config.toFirestore());
        }
      }

      print('Configuración de eCPM inicializada correctamente');
    } catch (e) {
      print('Error al inicializar configuración de eCPM: $e');
      throw e;
    }
  }

  /// Inicializa usuarios de prueba
  ///
  /// Crea usuarios en la colección users_v1 (para pruebas)
  /// NOTA: Para producción se usa la colección users, pero NUNCA la usamos en pruebas
  static Future<List<String>> _initializeUsers() async {
    try {
      final users = [
        {
          'id': 'sQGXQH1D3yR3cTqr4s8KuOaWmRF3',
          'email': '<EMAIL>',
          'phoneNumber': '+13109337405',
          'createAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 30))),
          'lastUpdateAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 2))),
          'lastLoginAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 1))),
          'country': 'US',
          'referralCode': 'REF000',
          'referralCount': 5,
          'referredBy': null,
          'totalReward': 25.0,
          'adEarnings': 0.0,
          'availableAdEarnings': 0.0,
          'isActive': true,
          'isEmailVerified': true,
          'isPhoneVerified': true,
          'completedRewardAds': 0,
        },
        {
          'id': 'tGxfgOKUsneurdKTjeemnEWcFMz1',
          'email': '<EMAIL>',
          'phoneNumber': '+5215512345678',
          'createAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 30))),
          'lastUpdateAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 2))),
          'lastLoginAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 1))),
          'country': 'MX',
          'referralCode': 'REF001',
          'referralCount': 3,
          'referredBy': null,
          'totalReward': 15.0,
          'adEarnings': 0.0,
          'availableAdEarnings': 0.0,
          'isActive': true,
          'isEmailVerified': true,
          'isPhoneVerified': true,
          'completedRewardAds': 0,
        },
        {
          'id': 'r9gxAxiioeWsjb4ror0JnhNZsqS2',
          'email': '<EMAIL>',
          'phoneNumber': '+5215587654321',
          'createAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 20))),
          'lastUpdateAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 1))),
          'lastLoginAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(hours: 12))),
          'country': 'MX',
          'referralCode': 'REF002',
          'referralCount': 1,
          'referredBy': 'user1',
          'totalReward': 5.0,
          'adEarnings': 0.0,
          'availableAdEarnings': 0.0,
          'isActive': true,
          'isEmailVerified': true,
          'isPhoneVerified': true,
          'completedRewardAds': 0,
        },
        {
          'id': 'FtlqSJE0hwSguE1nujU0LxXMpxH3',
          'email': '<EMAIL>',
          'createAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 10))),
          'lastUpdateAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(hours: 12))),
          'lastLoginAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(hours: 6))),
          'country': 'US',
          'referralCode': 'REF003',
          'referralCount': 0,
          'referredBy': 'user1',
          'totalReward': 0.0,
          'adEarnings': 0.0,
          'availableAdEarnings': 0.0,
          'isActive': true,
          'isEmailVerified': false,
          'isPhoneVerified': false,
          'completedRewardAds': 0,
        },
        {
          'id': '5vHV9MEC8oW0KTmxIlUnyZrwPT03',
          'email': '<EMAIL>',
          'createAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 5))),
          'lastUpdateAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 7))),
          'lastLoginAt': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 7))),
          'country': 'BR',
          'referralCode': 'REF004',
          'referralCount': 0,
          'referredBy': 'user1',
          'totalReward': 0.0,
          'adEarnings': 0.0,
          'availableAdEarnings': 0.0,
          'isActive': false,
          'isEmailVerified': false,
          'isPhoneVerified': false,
          'completedRewardAds': 0,
        },
      ];

      // Guardar usuarios en users
      for (var user in users) {
        final userId = user['id'] as String;

        // Asegurarse de que los campos createAt e isActive estén correctamente definidos
        if (user['createAt'] == null) {
          user['createAt'] = Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 30)));
        }
        if (user['isActive'] == null) {
          user['isActive'] = true;
        }

        print('Guardando usuario de prueba: $userId');
        // Guardar en users_v1 (sistema web para pruebas)
        await _firestore
            .collection('projects/pinataparty/users_v1')
            .doc(userId)
            .set(user);
      }

      print('Usuarios de prueba inicializados correctamente');
      return users.map((user) => user['id'] as String).toList();
    } catch (e) {
      print('Error al inicializar usuarios de prueba: $e');
      throw e;
    }
  }

  /// Inicializa impresiones de anuncios y actualiza estadísticas de usuarios
  static Future<void> _initializeAdImpressions(List<String> userIds) async {
    try {
      final adNetworks = ['AdMob', 'Unity', 'Meta'];
      final countries = [
        'US',
        'MX',
        'BR',
        'GB',
        'FR',
        'DE',
        'JP',
        'KR',
        'AU',
        'CA'
      ];
      // Reducir el rango de fechas a los últimos 30 días para generar menos datos
      final startDate = DateTime.now().subtract(const Duration(days: 30));
      final endDate = DateTime.now();

      // Mapa para acumular estadísticas por usuario
      final Map<String, Map<String, dynamic>> userStats = {};

      // Inicializar estadísticas para cada usuario
      for (final userId in userIds) {
        userStats[userId] = {
          'completedRewardAds': 0,
          'adEarnings': 0.0,
          'availableAdEarnings': 0.0,
        };
      }

      // Generar impresiones para cada usuario
      for (final userId in userIds) {
        // Obtener país del usuario (de la colección de prueba)
        final userDoc = await _firestore
            .collection('projects/pinataparty/users_v1')
            .doc(userId)
            .get();
        final userCountry = userDoc.data()?['country'] as String? ??
            countries[_random.nextInt(countries.length)];

        // Generar entre 5 y 15 impresiones por usuario (cantidad reducida para pruebas)
        final impressionsCount = 5 + _random.nextInt(10);

        for (int i = 0; i < impressionsCount; i++) {
          // Distribuir impresiones a lo largo del período
          final dayRange = endDate.difference(startDate).inDays;
          final randomDay = _random.nextInt(dayRange + 1);
          final timestamp = startDate.add(Duration(
            days: randomDay,
            hours: _random.nextInt(24),
            minutes: _random.nextInt(60),
          ));

          // Seleccionar red publicitaria aleatoria
          final adNetwork = adNetworks[_random.nextInt(adNetworks.length)];

          // Determinar si la impresión fue completada (90% de probabilidad)
          final completed = _random.nextDouble() < 0.9;

          // Crear la impresión
          final impression = {
            'userId': userId,
            'timestamp': Timestamp.fromDate(timestamp),
            'adNetwork': adNetwork,
            'adType': 'reward',
            'completed': completed,
            'country': userCountry,
            'appVersion': '1.${_random.nextInt(10)}.${_random.nextInt(10)}',
          };

          // Añadir rewardAmount si fue completado
          if (completed) {
            impression['rewardAmount'] = 5 + _random.nextInt(20);
          }

          // Guardar la impresión
          await _firestore
              .collection(
                  'projects/pinataparty/adAnalytics/impressions/records')
              .add(impression);

          // Actualizar estadísticas del usuario si fue completado
          if (completed) {
            // Incrementar contador de anuncios completados
            userStats[userId]!['completedRewardAds'] =
                userStats[userId]!['completedRewardAds'] + 1;

            // Calcular ingresos basados en eCPM
            final ecpmDoc = await _firestore
                .collection('projects/pinataparty/adAnalytics/config/eCPM')
                .doc('$adNetwork-reward')
                .get();

            if (ecpmDoc.exists) {
              final ecpmData = ecpmDoc.data()!;
              final countryRates =
                  ecpmData['countryRates'] as Map<String, dynamic>;
              final defaultRate = (ecpmData['defaultRate'] as num).toDouble();

              // Obtener tarifa para el país o usar la predeterminada
              final rate = countryRates[userCountry] != null
                  ? (countryRates[userCountry] as num).toDouble()
                  : defaultRate;

              // Calcular ingresos por impresión (eCPM / 1000)
              final revenue = rate / 1000;

              // Actualizar ingresos del usuario
              userStats[userId]!['adEarnings'] =
                  userStats[userId]!['adEarnings'] + revenue;
              userStats[userId]!['availableAdEarnings'] =
                  userStats[userId]!['availableAdEarnings'] + (revenue * 0.5);
            }
          }

          // Actualizar historial diario en la colección users
          if (completed) {
            final year = timestamp.year.toString();
            final month = timestamp.month.toString().padLeft(2, '0');
            final day = timestamp.day.toString().padLeft(2, '0');

            final dailyRef = _firestore.doc(
                'projects/pinataparty/users_v1/$userId/adHistory/$year/$month/$day');

            await dailyRef.set({
              'completedRewardAds': FieldValue.increment(1),
              'timestamp': Timestamp.fromDate(timestamp),
            }, SetOptions(merge: true));
          }
        }

        // Actualizar estadísticas totales del usuario (en la colección de prueba)
        await _firestore
            .collection('projects/pinataparty/users_v1')
            .doc(userId)
            .update({
          'completedRewardAds': userStats[userId]!['completedRewardAds'],
          'adEarnings': userStats[userId]!['adEarnings'],
          'availableAdEarnings': userStats[userId]!['availableAdEarnings'],
          'lastUpdateAt': FieldValue.serverTimestamp(),
        });
      }

      print('Impresiones de anuncios inicializadas correctamente');
    } catch (e) {
      print('Error al inicializar impresiones de anuncios: $e');
      throw e;
    }
  }

  /// Inicializa retiros de prueba
  static Future<void> _initializeWithdrawals(List<String> userIds) async {
    try {
      // Obtener datos de usuarios para los retiros (de la colección de prueba)
      final userDocs = await Future.wait(userIds.map((id) => _firestore
          .collection('projects/pinataparty/users_v1')
          .doc(id)
          .get()));

      // Filtrar usuarios que tienen email y teléfono verificados
      final verifiedUserIndices = <int>[];
      for (int i = 0; i < userIds.length; i++) {
        final userData = userDocs[i].data()!;
        final isEmailVerified = userData['isEmailVerified'] as bool? ?? false;
        final isPhoneVerified = userData['isPhoneVerified'] as bool? ?? false;

        if (isEmailVerified && isPhoneVerified) {
          verifiedUserIndices.add(i);
        }
      }

      // Si no hay usuarios verificados, usar el primer usuario (para pruebas)
      if (verifiedUserIndices.isEmpty) {
        verifiedUserIndices.add(0);
      }

      final withdrawals = [
        {
          'id': 'withdrawal1',
          'userId': userIds[verifiedUserIndices[0]],
          'userEmail': userDocs[verifiedUserIndices[0]].data()!['email'],
          'userCountry': userDocs[verifiedUserIndices[0]].data()!['country'],
          'userPhone': userDocs[verifiedUserIndices[0]].data()!['phoneNumber'],
          'amount': 20.00,
          'requestDate': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 3))),
          'status': 'pending',
          'type': 'mobile_recharge',
          'details': {
            'provider': 'Telcel',
            'phoneNumber':
                userDocs[verifiedUserIndices[0]].data()!['phoneNumber'],
          },
        },
        {
          'id': 'withdrawal2',
          'userId': userIds[verifiedUserIndices.length > 1
              ? verifiedUserIndices[1]
              : verifiedUserIndices[0]],
          'userEmail': userDocs[verifiedUserIndices.length > 1
                  ? verifiedUserIndices[1]
                  : verifiedUserIndices[0]]
              .data()!['email'],
          'userCountry': userDocs[verifiedUserIndices.length > 1
                  ? verifiedUserIndices[1]
                  : verifiedUserIndices[0]]
              .data()!['country'],
          'userPhone': userDocs[verifiedUserIndices.length > 1
                  ? verifiedUserIndices[1]
                  : verifiedUserIndices[0]]
              .data()!['phoneNumber'],
          'amount': 15.00,
          'requestDate': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 5))),
          'status': 'approved',
          'type': 'gift_card',
          'details': {
            'provider': 'Amazon',
            'email': userDocs[verifiedUserIndices.length > 1
                    ? verifiedUserIndices[1]
                    : verifiedUserIndices[0]]
                .data()!['email'],
          },
          'processedDate': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 4))),
          'processedBy': 'admin',
        },
        {
          'id': 'withdrawal3',
          'userId': userIds[verifiedUserIndices[0]],
          'userEmail': userDocs[verifiedUserIndices[0]].data()!['email'],
          'userCountry': userDocs[verifiedUserIndices[0]].data()!['country'],
          'userPhone': userDocs[verifiedUserIndices[0]].data()!['phoneNumber'],
          'amount': 10.00,
          'requestDate': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 7))),
          'status': 'rejected',
          'type': 'mobile_recharge',
          'details': {
            'provider': 'Movistar',
            'phoneNumber':
                userDocs[verifiedUserIndices[0]].data()!['phoneNumber'],
          },
          'processedDate': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 6))),
          'processedBy': 'admin',
          'rejectionReason': 'Número de teléfono inválido',
        },
      ];

      // Si hay un usuario de EE.UU. verificado, agregar un retiro pendiente para él
      final usUserIndex = verifiedUserIndices
          .indexWhere((index) => userDocs[index].data()!['country'] == 'US');

      if (usUserIndex >= 0) {
        final verifiedUsUserIndex = verifiedUserIndices[usUserIndex];
        withdrawals.add({
          'id': 'withdrawal4',
          'userId': userIds[verifiedUsUserIndex],
          'userEmail': userDocs[verifiedUsUserIndex].data()!['email'],
          'userCountry': 'US',
          'amount': 15.00,
          'requestDate': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 3))),
          'status': 'pending',
          'type': 'gift_card',
          'details': {
            'provider': 'Google Play',
            'email': userDocs[verifiedUsUserIndex].data()!['email'],
          },
        });
      }

      // Guardar retiros
      for (var withdrawal in withdrawals) {
        await _firestore
            .collection('projects/pinataparty/withdrawals')
            .doc(withdrawal['id'] as String)
            .set(withdrawal);
      }

      print('Retiros de prueba inicializados correctamente');
    } catch (e) {
      print('Error al inicializar retiros de prueba: $e');
      throw e;
    }
  }

  /// Inicializa logs administrativos
  static Future<void> _initializeAdminLogs() async {
    try {
      final withdrawalSettings =
          await _firestore.doc(WithdrawalSettings.documentPath()).get();

      final logs = [
        {
          'adminId': 'admin',
          'adminEmail': '<EMAIL>',
          'action': 'approve_withdrawal',
          'timestamp': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 4))),
          'details': {
            'withdrawalId': 'withdrawal2',
            'userId': 'user2',
            'amount': 15.00,
            'type': 'gift_card',
          },
        },
        {
          'adminId': 'admin',
          'adminEmail': '<EMAIL>',
          'action': 'reject_withdrawal',
          'timestamp': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 6))),
          'details': {
            'withdrawalId': 'withdrawal3',
            'userId': 'user1',
            'amount': 10.00,
            'reason': 'Número de teléfono inválido',
          },
        },
        {
          'adminId': 'admin',
          'adminEmail': '<EMAIL>',
          'action': 'update_withdrawal_settings',
          'timestamp': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 10))),
          'details': withdrawalSettings.data(),
        },
        {
          'adminId': 'admin',
          'adminEmail': '<EMAIL>',
          'action': 'deactivate_user',
          'timestamp': Timestamp.fromDate(
              DateTime.now().subtract(const Duration(days: 5))),
          'details': {
            'userId': 'user4',
          },
        },
      ];

      // Guardar logs
      for (var log in logs) {
        await _firestore.collection('projects/pinataparty/adminLogs').add(log);
      }

      print('Logs administrativos inicializados correctamente');
    } catch (e) {
      print('Error al inicializar logs administrativos: $e');
      throw e;
    }
  }

  /// Limpia los datos de prueba generados por TestDataService
  ///
  /// Este método elimina los datos de prueba generados por TestDataService,
  /// pero NUNCA elimina las colecciones principales de producción como
  /// 'projects', 'pinataparty', 'users', 'referrals', 'referralsCode'.
  /// Útil para reiniciar el sistema a un estado limpio para pruebas.
  static Future<void> cleanAllTestData({
    Function(String message, double progress)? onProgress,
  }) async {
    try {
      onProgress?.call('Iniciando limpieza de datos de prueba...', 0.0);

      // ⚠️ ADVERTENCIA: NUNCA eliminar las colecciones principales de producción
      // - projects
      // - pinataparty
      // - users (colección de producción, NUNCA eliminar)
      // - referrals
      // - referralsCode
      //
      // Nota: Para pruebas usamos users_v1 en lugar de users

      // 1. Limpiar usuarios de prueba (solo los creados por TestDataService)
      onProgress?.call('Limpiando usuarios de prueba...', 0.1);
      final testUserIds = [
        'sQGXQH1D3yR3cTqr4s8KuOaWmRF3',
        'user1',
        'user2',
        'user3',
        'user4'
      ];
      for (final userId in testUserIds) {
        await _firestore
            .collection('projects/pinataparty/users_v1')
            .doc(userId)
            .delete();
      }

      // 2. Limpiar retiros
      onProgress?.call('Limpiando retiros...', 0.3);
      await _deleteCollection('projects/pinataparty/withdrawals');

      // 3. Limpiar impresiones de anuncios
      onProgress?.call('Limpiando impresiones de anuncios...', 0.5);
      await _deleteCollection(
          'projects/pinataparty/adAnalytics/impressions/records');

      // 4. Limpiar configuración de eCPM
      onProgress?.call('Limpiando configuración de eCPM...', 0.7);
      await _deleteCollection('projects/pinataparty/adAnalytics/config/eCPM');

      // 5. Limpiar configuración de retiros
      onProgress?.call('Limpiando configuración de retiros...', 0.8);
      await _firestore.doc(WithdrawalSettings.documentPath()).delete();

      // 6. Limpiar logs administrativos
      onProgress?.call('Limpiando logs administrativos...', 0.9);
      await _deleteCollection('projects/pinataparty/adminLogs');

      onProgress?.call('Datos de prueba limpiados correctamente', 1.0);
      print('Todos los datos de prueba han sido limpiados correctamente');
    } catch (e) {
      print('Error al limpiar datos de prueba: $e');
      throw Exception('Error al limpiar datos de prueba: $e');
    }
  }

  /// Elimina todos los documentos de una colección
  ///
  /// IMPORTANTE: Este método incluye verificaciones de seguridad para evitar
  /// eliminar colecciones críticas de producción.
  static Future<void> _deleteCollection(String collectionPath) async {
    // Lista de colecciones protegidas que NUNCA deben ser eliminadas
    final protectedCollections = [
      'projects',
      'pinataparty',
      'users',
      'referrals',
      'referralsCode',
    ];

    // Verificar si la ruta contiene alguna colección protegida como colección raíz
    for (final protected in protectedCollections) {
      if (collectionPath == protected) {
        print(
            '\n\n⚠️ ADVERTENCIA: Intento de eliminar una colección protegida: $collectionPath');
        print(
            'Esta colección está protegida y no se eliminará para evitar pérdida de datos.');
        print(
            'Si necesitas eliminar documentos específicos, hazlo de forma individual.\n\n');
        return; // No eliminar la colección protegida
      }
    }

    // Proceder con la eliminación de documentos
    final collection = await _firestore.collection(collectionPath).get();

    for (final doc in collection.docs) {
      await doc.reference.delete();
    }
  }
}
