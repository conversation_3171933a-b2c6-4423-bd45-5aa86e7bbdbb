import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CacheService {
  // Claves para almacenamiento
  static const String _analyticsDataKey = 'analytics_data';
  static const String _userAnalyticsPrefix = 'user_analytics_';

  // Guardar datos de analítica global en caché
  static Future<void> cacheAnalyticsData(Map<String, dynamic> data) async {
    try {
      print('CacheService: Guardando datos de analytics en caché');
      final prefs = await SharedPreferences.getInstance();

      // Verificar que los datos sean válidos
      if (data.containsKey('startDate') && data.containsKey('endDate')) {
        // Agregar timestamp de caché para validación
        data['cachedAt'] = DateTime.now().toIso8601String();

        final jsonData = jsonEncode(data);
        await prefs.setString(_analyticsDataKey, jsonData);
        print('CacheService: Datos guardados correctamente en caché');
      } else {
        print('CacheService: Error - Datos incompletos, no se guardarán en caché');
      }
    } catch (e) {
      print('CacheService: Error al guardar datos en caché: $e');
    }
  }

  // Obtener datos de analítica global desde caché
  static Future<Map<String, dynamic>?> getCachedAnalytics() async {
    try {
      print('CacheService: Intentando obtener datos de analytics desde caché');
      final prefs = await SharedPreferences.getInstance();
      final jsonData = prefs.getString(_analyticsDataKey);

      if (jsonData == null) {
        print('CacheService: No hay datos en caché');
        return null;
      }

      // Decodificar los datos
      final data = jsonDecode(jsonData) as Map<String, dynamic>;

      // Verificar si los datos tienen más de 24 horas (opcional, para forzar actualización periódica)
      if (data.containsKey('cachedAt')) {
        final cachedAt = DateTime.parse(data['cachedAt'] as String);
        final now = DateTime.now();
        final difference = now.difference(cachedAt).inHours;

        if (difference > 24) {
          print('CacheService: Datos en caché tienen más de 24 horas, se recomienda actualizar');
        }
      }

      print('CacheService: Datos obtenidos correctamente desde caché');
      return data;
    } catch (e) {
      print('CacheService: Error al obtener datos de caché: $e');
      // En caso de error, limpiar la caché para evitar problemas futuros
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_analyticsDataKey);
        print('CacheService: Caché limpiada debido a error');
      } catch (_) {}
      return null;
    }
  }

  // Guardar datos de analítica de usuario en caché
  static Future<void> cacheUserAnalytics(String userId, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _userAnalyticsPrefix + userId;
      final jsonData = jsonEncode(data);
      await prefs.setString(key, jsonData);
    } catch (e) {
      print('Error al guardar datos de usuario en caché: $e');
    }
  }

  // Obtener datos de analítica de usuario desde caché
  static Future<Map<String, dynamic>?> getCachedUserAnalytics(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _userAnalyticsPrefix + userId;
      final jsonData = prefs.getString(key);

      if (jsonData == null) {
        return null;
      }

      return jsonDecode(jsonData) as Map<String, dynamic>;
    } catch (e) {
      print('Error al obtener datos de usuario de caché: $e');
      return null;
    }
  }

  // Limpiar todos los datos en caché
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      print('Error al limpiar caché: $e');
    }
  }
}