import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';
import '../models/user_data.dart';
import 'firebase_service.dart';
import 'analytics_service.dart';
import 'test_data_service.dart';

/// Servicio para gestionar funcionalidades administrativas
class AdminService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Verificar si el usuario actual es administrador
  static bool get isUserAdmin => FirebaseService.isUserAdmin;

  // Obtener todos los retiros pendientes
  static Future<List<WithdrawalManagement>> getPendingWithdrawals() async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      final querySnapshot = await _firestore
          .collection(WithdrawalManagement.collectionPath())
          .where('status', isEqualTo: 'pending')
          .orderBy('requestDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => WithdrawalManagement.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>, null))
          .toList();
    } catch (e) {
      // Capturar y registrar errores de índice
      if (e.toString().contains('index')) {
        print('ERROR DE ÍNDICE: $e');
        _logIndexError('withdrawals', ['status', 'requestDate'],
            'Error al obtener retiros pendientes. Se requiere un índice compuesto.');
      }
      throw Exception('Error al obtener retiros pendientes: $e');
    }
  }

  // Obtener historial de retiros procesados
  static Future<List<WithdrawalManagement>> getProcessedWithdrawals({
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      print(
          'Obteniendo historial de retiros con filtros: status=$status, startDate=$startDate, endDate=$endDate');

      // Verificar si estamos filtrando por el día actual
      final now = DateTime.now();
      final isCurrentDayFilter = startDate != null &&
          endDate != null &&
          _isSameDay(startDate, now) &&
          _isSameDay(endDate, now);

      // Primero obtenemos todos los retiros y luego filtramos en memoria
      // Esto evita problemas con índices compuestos en Firestore
      Query query =
          _firestore.collection(WithdrawalManagement.collectionPath());

      // Aplicamos solo el filtro de estado, que tiene un índice simple
      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }

      // Ordenamos por fecha de solicitud (más reciente primero)
      query = query.orderBy('requestDate', descending: true);

      // Si estamos filtrando por el día actual, limitamos a los 50 más recientes
      // para evitar cargar toda la base de datos
      if (isCurrentDayFilter) {
        query = query.limit(50);
      }

      // Ejecutamos la consulta
      final querySnapshot = await query.get();
      print(
          'Consulta completada. Documentos encontrados: ${querySnapshot.docs.length}');

      // Convertimos los documentos a objetos WithdrawalManagement
      List<WithdrawalManagement> withdrawals = querySnapshot.docs
          .map((doc) => WithdrawalManagement.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>, null))
          .toList();

      // Si no se especificó un estado, filtramos los pendientes en memoria
      if (status == null) {
        withdrawals = withdrawals.where((w) => w.status != 'pending').toList();
      }

      // Aplicamos filtros de fecha
      if (startDate != null) {
        withdrawals = withdrawals
            .where((w) =>
                w.requestDate.isAfter(startDate) ||
                _isSameDay(w.requestDate, startDate))
            .toList();
      }

      if (endDate != null) {
        // Ajustamos el endDate para incluir todo el día
        final adjustedEndDate =
            DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
        withdrawals = withdrawals
            .where((w) =>
                w.requestDate.isBefore(adjustedEndDate) ||
                _isSameDay(w.requestDate, endDate))
            .toList();
      }

      // Si estamos filtrando por el día actual y no hay resultados,
      // mostramos un mensaje informativo en los logs
      if (isCurrentDayFilter && withdrawals.isEmpty) {
        print(
            'No se encontraron retiros para el día actual (${DateFormat('dd/MM/yyyy').format(now)})');
      }

      print('Después de filtrar: ${withdrawals.length} retiros');
      return withdrawals;
    } catch (e) {
      print('Error detallado al obtener historial de retiros: $e');
      throw Exception('Error al obtener historial de retiros: $e');
    }
  }

  // Verifica si dos fechas corresponden al mismo día
  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  // Aprobar un retiro
  static Future<void> approveWithdrawal(String withdrawalId) async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      final withdrawalDoc = await _firestore
          .collection(WithdrawalManagement.collectionPath())
          .doc(withdrawalId)
          .get();

      if (!withdrawalDoc.exists) {
        throw Exception('El retiro no existe');
      }

      final withdrawal = WithdrawalManagement.fromFirestore(
          withdrawalDoc as DocumentSnapshot<Map<String, dynamic>>, null);

      if (withdrawal.status != 'pending') {
        throw Exception('Este retiro ya ha sido procesado');
      }

      // Actualizar el estado del retiro
      await _firestore
          .collection(WithdrawalManagement.collectionPath())
          .doc(withdrawalId)
          .update({
        'status': 'approved',
        'processedDate': FieldValue.serverTimestamp(),
        'processedBy': _auth.currentUser!.uid,
      });

      // Registrar la aprobación en el documento del usuario
      await _firestore
          .collection('projects/pinataparty/users')
          .doc(withdrawal.userId)
          .update({
        'lastUpdateAt': FieldValue.serverTimestamp(),
        'lastApprovedWithdrawalDate': FieldValue.serverTimestamp(),
        'lastApprovedWithdrawalAmount': withdrawal.amount,
        'totalApprovedWithdrawals': FieldValue.increment(1),
        'totalWithdrawnAmount': FieldValue.increment(withdrawal.amount),
      });

      // Registrar la acción en los logs
      await _firestore.collection(AdminLog.collectionPath()).add({
        'adminId': _auth.currentUser!.uid,
        'adminEmail': _auth.currentUser!.email,
        'action': 'approve_withdrawal',
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'withdrawalId': withdrawalId,
          'userId': withdrawal.userId,
          'amount': withdrawal.amount,
          'type': withdrawal.type,
        },
      });
    } catch (e) {
      throw Exception('Error al aprobar retiro: $e');
    }
  }

  // Rechazar un retiro
  static Future<void> rejectWithdrawal(
      String withdrawalId, String reason) async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      final withdrawalDoc = await _firestore
          .collection(WithdrawalManagement.collectionPath())
          .doc(withdrawalId)
          .get();

      if (!withdrawalDoc.exists) {
        throw Exception('El retiro no existe');
      }

      final withdrawal = WithdrawalManagement.fromFirestore(
          withdrawalDoc as DocumentSnapshot<Map<String, dynamic>>, null);

      if (withdrawal.status != 'pending') {
        throw Exception('Este retiro ya ha sido procesado');
      }

      // Actualizar el estado del retiro
      await _firestore
          .collection(WithdrawalManagement.collectionPath())
          .doc(withdrawalId)
          .update({
        'status': 'rejected',
        'processedDate': FieldValue.serverTimestamp(),
        'processedBy': _auth.currentUser!.uid,
        'rejectionReason': reason,
      });

      // Registrar la acción en los logs
      await _firestore.collection(AdminLog.collectionPath()).add({
        'adminId': _auth.currentUser!.uid,
        'adminEmail': _auth.currentUser!.email,
        'action': 'reject_withdrawal',
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'withdrawalId': withdrawalId,
          'userId': withdrawal.userId,
          'amount': withdrawal.amount,
          'reason': reason,
        },
      });

      // Devolver el saldo al usuario y registrar la devolución
      await _firestore
          .collection('projects/pinataparty/users')
          .doc(withdrawal.userId)
          .update({
        'availableAdEarnings': FieldValue.increment(withdrawal.amount),
        'lastUpdateAt': FieldValue.serverTimestamp(),
        'lastRejectedWithdrawalDate': FieldValue.serverTimestamp(),
        'lastRejectedWithdrawalAmount': withdrawal.amount,
        'lastRejectedWithdrawalReason': reason,
      });
    } catch (e) {
      throw Exception('Error al rechazar retiro: $e');
    }
  }

  // Obtener configuración de retiros
  static Future<WithdrawalSettings> getWithdrawalSettings() async {
    try {
      final doc = await _firestore.doc(WithdrawalSettings.documentPath()).get();

      if (doc.exists) {
        return WithdrawalSettings.fromFirestore(
            doc as DocumentSnapshot<Map<String, dynamic>>, null);
      } else {
        // Si no existe, crear con valores por defecto
        final defaultSettings = WithdrawalSettings.defaultSettings();
        await _firestore
            .doc(WithdrawalSettings.documentPath())
            .set(defaultSettings.toFirestore());
        return defaultSettings;
      }
    } catch (e) {
      throw Exception('Error al obtener configuración de retiros: $e');
    }
  }

  // Actualizar configuración de retiros
  static Future<void> updateWithdrawalSettings(
      WithdrawalSettings settings) async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      await _firestore
          .doc(WithdrawalSettings.documentPath())
          .set(settings.toFirestore());

      // Registrar la acción en los logs
      await _firestore.collection(AdminLog.collectionPath()).add({
        'adminId': _auth.currentUser!.uid,
        'adminEmail': _auth.currentUser!.email,
        'action': 'update_withdrawal_settings',
        'timestamp': FieldValue.serverTimestamp(),
        'details': settings.toFirestore(),
      });
    } catch (e) {
      throw Exception('Error al actualizar configuración de retiros: $e');
    }
  }

  // Obtener lista de usuarios - versión ultra simple
  static Future<List<UserManagement>> getUsers({
    String? country,
    bool? isActive,
    int limit = 50,
    DocumentSnapshot? startAfter,
  }) async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      print('SOLUCIÓN SIMPLE: Obteniendo todos los usuarios sin filtros');

      // Colección de usuarios
      final collectionPath = 'projects/pinataparty/users';

      // Obtener todos los usuarios sin filtros
      final snapshot = await _firestore.collection(collectionPath).get();

      print('Total de documentos obtenidos: ${snapshot.docs.length}');

      // Convertir documentos a objetos UserManagement
      List<UserManagement> allUsers = [];

      for (var doc in snapshot.docs) {
        try {
          // Usar el constructor fromFirestore para asegurar consistencia
          final user = UserManagement.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>, null);

          allUsers.add(user);
        } catch (e) {
          print('Error al convertir documento ${doc.id}: $e');
        }
      }

      print('Total de usuarios convertidos: ${allUsers.length}');

      // Mostrar todos los usuarios para diagnóstico
      print('Listado de todos los usuarios:');
      for (var user in allUsers) {
        print(
            'ID: ${user.id}, Email: ${user.email}, Country: ${user.country}, isActive: ${user.isActive}');
      }

      // Si no hay filtros, devolver todos los usuarios
      if (country == null && isActive == null) {
        print('No hay filtros aplicados, devolviendo todos los usuarios');
        return allUsers;
      }

      // Aplicar filtros
      List<UserManagement> result = allUsers;

      // Filtrar por país
      if (country != null && country.isNotEmpty) {
        print('Filtrando por país: $country');
        result = result.where((user) {
          if (user.country.isEmpty) return false;
          return user.country.toUpperCase() == country.toUpperCase();
        }).toList();
        print('Usuarios después de filtrar por país: ${result.length}');
      }

      // Filtrar por estado activo
      if (isActive != null) {
        print('Filtrando por estado activo: $isActive');
        result = result.where((user) => user.isActive == isActive).toList();
        print(
            'Usuarios después de filtrar por estado activo: ${result.length}');
      }

      print('Total de usuarios después de aplicar filtros: ${result.length}');

      return result;
    } catch (e) {
      print('Error detallado al obtener usuarios: $e');
      // Capturar y registrar errores de índice
      if (e.toString().contains('index')) {
        print('ERROR DE ÍNDICE: $e');
        List<String> fields = ['country', 'isActive', 'createAt'];
        _logIndexError('users', fields,
            'Error al obtener usuarios. Se requiere un índice compuesto.');
      }
      throw Exception('Error al obtener usuarios: $e');
    }
  }

  // Obtener detalle de un usuario
  static Future<UserProfile> getUserDetail(String userId) async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      final doc = await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .get();

      if (!doc.exists) {
        throw Exception('El usuario no existe');
      }

      // Usar UserProfile para tener acceso a campos adicionales como estimatedEarnings y lastDistributionDate
      return UserProfile.fromFirestore(doc);
    } catch (e) {
      throw Exception('Error al obtener detalle de usuario: $e');
    }
  }

  // Cambiar estado de activación de un usuario
  static Future<void> toggleUserStatus(String userId, bool isActive) async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .update({
        'isActive': isActive,
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });

      // Registrar la acción en los logs
      await _firestore.collection(AdminLog.collectionPath()).add({
        'adminId': _auth.currentUser!.uid,
        'adminEmail': _auth.currentUser!.email,
        'action': isActive ? 'activate_user' : 'deactivate_user',
        'timestamp': FieldValue.serverTimestamp(),
        'details': {
          'userId': userId,
        },
      });
    } catch (e) {
      throw Exception('Error al cambiar estado de usuario: $e');
    }
  }

  // Obtener historial de retiros de un usuario
  static Future<List<WithdrawalManagement>> getUserWithdrawals(
      String userId) async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      final querySnapshot = await _firestore
          .collection(WithdrawalManagement.collectionPath())
          .where('userId', isEqualTo: userId)
          .orderBy('requestDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => WithdrawalManagement.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>, null))
          .toList();
    } catch (e) {
      // Capturar y registrar errores de índice
      if (e.toString().contains('index')) {
        print('ERROR DE ÍNDICE: $e');
        _logIndexError('withdrawals', ['userId', 'requestDate'],
            'Error al obtener historial de retiros del usuario. Se requiere un índice compuesto.');
      }
      throw Exception('Error al obtener historial de retiros del usuario: $e');
    }
  }

  // Obtener logs de administración
  static Future<List<AdminLog>> getAdminLogs({
    String? action,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
  }) async {
    if (!isUserAdmin) {
      throw Exception('No tienes permisos para realizar esta acción');
    }

    try {
      Query query = _firestore.collection(AdminLog.collectionPath());

      if (action != null) {
        query = query.where('action', isEqualTo: action);
      }

      if (startDate != null) {
        query = query.where('timestamp',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('timestamp',
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      // Ordenar por fecha (más reciente primero)
      query = query.orderBy('timestamp', descending: true);

      // Limitar resultados
      query = query.limit(limit);

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => AdminLog.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>, null))
          .toList();
    } catch (e) {
      throw Exception('Error al obtener logs de administración: $e');
    }
  }

  /// Registra un error de índice y muestra instrucciones para solucionarlo
  static void _logIndexError(
      String collection, List<String> fields, String message) {
    final indexDefinition = {
      "collectionGroup": collection,
      "queryScope": "COLLECTION",
      "fields": fields
          .map((field) => {
                "fieldPath": field,
                "order": field == fields.last ? "DESCENDING" : "ASCENDING"
              })
          .toList()
    };

    print('\n\n=== ERROR DE ÍNDICE REQUERIDO ===');
    print(message);
    print('\nSe requiere crear un índice compuesto en Firebase Console.');
    print('\nPuedes crear el índice manualmente en:');
    print(
        'https://console.firebase.google.com/project/[TU-PROYECTO]/firestore/indexes');

    print('\nDefinición del índice:');
    print(indexDefinition);

    print(
        '\nO puedes usar el archivo firestore.indexes.json incluido en el proyecto');
    print('y ejecutar: firebase deploy --only firestore:indexes');
    print('===================================\n\n');
  }
}
