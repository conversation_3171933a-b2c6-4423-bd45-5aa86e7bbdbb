import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/models.dart';

/// Servicio para gestionar las distribuciones de ganancias
class EarningsDistributionService {
  final FirebaseFirestore _firestore;

  EarningsDistributionService(this._firestore);

  /// Ruta de la colección de distribuciones de ganancias
  String get _collectionPath => 'projects/pinataparty/earningsDistributions';

  /// Crea una nueva distribución de ganancias
  ///
  /// Retorna el ID de la distribución creada
  Future<String> createDistribution({
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, Map<String, double>> ecpmRates, // Por red y país
    required String adminId,
    required String adminEmail,
  }) async {
    try {
      // Crear el documento de distribución
      final distributionRef = await _firestore.collection(_collectionPath).add({
        'startDate': Timestamp.fromDate(startDate),
        'endDate': Timestamp.fromDate(endDate),
        'distributionDate': FieldValue.serverTimestamp(),
        'adminId': adminId,
        'adminEmail': adminEmail,
        'ecpmRates': ecpmRates,
        'totalProcessedAds': 0,
        'totalDistributedEarnings': 0.0,
        'processedUserIds': [],
        'status': 'pending',
      });

      return distributionRef.id;
    } catch (e) {
      print('Error al crear distribución: $e');
      throw e;
    }
  }

  /// Procesa la distribución de ganancias
  ///
  /// Este método calcula y distribuye las ganancias para todos los usuarios
  /// en el rango de fechas especificado, utilizando los valores de eCPM configurados.
  Future<void> processDistribution(String distributionId) async {
    try {
      // Actualizar estado a 'processing'
      await _firestore
          .collection(_collectionPath)
          .doc(distributionId)
          .update({'status': 'processing'});

      // Obtener datos de la distribución
      final distributionDoc = await _firestore
          .collection(_collectionPath)
          .doc(distributionId)
          .get();

      if (!distributionDoc.exists) {
        throw Exception('Distribución no encontrada');
      }

      final distributionData = distributionDoc.data()!;
      final startDate = (distributionData['startDate'] as Timestamp).toDate();
      final endDate = (distributionData['endDate'] as Timestamp).toDate();
      final ecpmRates = distributionData['ecpmRates'] as Map<String, dynamic>;

      // Obtener todos los usuarios activos
      final usersQuery = await _firestore
          .collection('projects/pinataparty/users')
          .where('isActive', isEqualTo: true)
          .get();

      int totalProcessedAds = 0;
      double totalDistributedEarnings = 0.0;
      List<String> processedUserIds = [];

      // Procesar cada usuario
      for (var userDoc in usersQuery.docs) {
        final userId = userDoc.id;
        final userData = userDoc.data();
        final userCountry = userData['country'] as String? ?? 'default';

        // Obtener impresiones de anuncios en el rango de fechas
        final impressionsQuery = await _firestore
            .collection('projects/pinataparty/adAnalytics/impressions/records')
            .where('userId', isEqualTo: userId)
            .where('timestamp',
                isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
            .where('timestamp',
                isLessThanOrEqualTo: Timestamp.fromDate(endDate))
            .where('completed', isEqualTo: true)
            .get();

        // Calcular ganancias por red
        Map<String, int> adsByNetwork = {};
        double userEarnings = 0.0;

        for (var impressionDoc in impressionsQuery.docs) {
          final impressionData = impressionDoc.data();
          final adNetwork = impressionData['adNetwork'] as String? ?? 'default';

          // Incrementar contador para esta red
          adsByNetwork[adNetwork] = (adsByNetwork[adNetwork] ?? 0) + 1;

          // Obtener tarifa eCPM para esta red y país
          final networkRates = ecpmRates[adNetwork] as Map<String, dynamic>? ??
              ecpmRates['default'] as Map<String, dynamic>;
          final ecpmRate = networkRates[userCountry] as double? ??
              networkRates['default'] as double;

          // Calcular ganancia para esta impresión
          userEarnings += ecpmRate / 1000;
        }

        // Calcular ganancias disponibles para retiro (50%)
        final availableEarnings = userEarnings * 0.5;

        // Actualizar ganancias del usuario
        if (impressionsQuery.docs.isNotEmpty) {
          await _firestore
              .collection('projects/pinataparty/users')
              .doc(userId)
              .update({
            'adEarnings': FieldValue.increment(userEarnings),
            'availableAdEarnings': FieldValue.increment(availableEarnings),
            'estimatedEarnings': 0.0, // Resetear ganancias estimadas
            'lastDistributionDate': Timestamp.fromDate(endDate),
            'lastUpdateAt': FieldValue.serverTimestamp(),
          });

          // Actualizar contadores
          totalProcessedAds += impressionsQuery.docs.length;
          totalDistributedEarnings += userEarnings;
          processedUserIds.add(userId);
        }
      }

      // Actualizar documento de distribución
      await _firestore.collection(_collectionPath).doc(distributionId).update({
        'status': 'completed',
        'totalProcessedAds': totalProcessedAds,
        'totalDistributedEarnings': totalDistributedEarnings,
        'processedUserIds': processedUserIds,
        'completedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // Marcar como fallido en caso de error
      await _firestore.collection(_collectionPath).doc(distributionId).update({
        'status': 'failed',
        'errorMessage': e.toString(),
      });

      print('Error al procesar distribución: $e');
      throw e;
    }
  }

  /// Obtiene una distribución por su ID
  Future<EarningsDistribution?> getDistribution(String distributionId) async {
    try {
      final doc = await _firestore
          .collection(_collectionPath)
          .doc(distributionId)
          .get();

      if (!doc.exists) {
        return null;
      }

      return EarningsDistribution.fromFirestore(doc);
    } catch (e) {
      print('Error al obtener distribución: $e');
      return null;
    }
  }

  /// Obtiene todas las distribuciones
  Future<List<EarningsDistribution>> getAllDistributions() async {
    try {
      final query = await _firestore
          .collection(_collectionPath)
          .orderBy('distributionDate', descending: true)
          .get();

      return query.docs
          .map((doc) => EarningsDistribution.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error al obtener distribuciones: $e');
      return [];
    }
  }

  /// Calcula las ganancias estimadas para un usuario
  ///
  /// Este método calcula las ganancias estimadas para un usuario desde su última
  /// distribución, utilizando valores conservadores de eCPM.
  Future<double> calculateEstimatedEarnings(UserProfile userProfile) async {
    try {
      // Obtener la fecha de la última distribución
      final DateTime lastDistribution =
          userProfile.lastDistributionDate ?? DateTime(2000);

      // Obtener impresiones desde la última distribución
      final impressionsQuery = await _firestore
          .collection('projects/pinataparty/adAnalytics/impressions/records')
          .where('userId', isEqualTo: userProfile.id)
          .where('timestamp',
              isGreaterThan: Timestamp.fromDate(lastDistribution))
          .where('completed', isEqualTo: true)
          .get();

      // Valores conservadores de eCPM por red y país
      final Map<String, Map<String, double>> conservativeRates = {
        'AdMob': {'US': 2.0, 'MX': 1.0, 'default': 0.5},
        'Unity': {'US': 1.5, 'MX': 0.8, 'default': 0.4},
        'Meta': {'US': 1.0, 'MX': 0.5, 'default': 0.3},
        'default': {'US': 1.0, 'MX': 0.5, 'default': 0.3},
      };

      // Calcular ganancias estimadas
      double estimatedEarnings = 0.0;

      for (var doc in impressionsQuery.docs) {
        final data = doc.data();
        final adNetwork = data['adNetwork'] as String? ?? 'default';

        // Obtener tarifa para esta red y país
        final networkRates =
            conservativeRates[adNetwork] ?? conservativeRates['default']!;
        final ecpmRate =
            networkRates[userProfile.country] ?? networkRates['default']!;

        // Calcular ganancia estimada para esta impresión
        estimatedEarnings += ecpmRate / 1000;
      }

      return estimatedEarnings;
    } catch (e) {
      print('Error al calcular ganancias estimadas: $e');
      return 0.0;
    }
  }

  /// Actualiza las ganancias estimadas de un usuario
  Future<void> updateEstimatedEarnings(UserProfile userProfile) async {
    try {
      final estimatedEarnings = await calculateEstimatedEarnings(userProfile);

      // Actualizar ganancias estimadas del usuario
      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userProfile.id)
          .update({
        'estimatedEarnings': estimatedEarnings,
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error al actualizar ganancias estimadas: $e');
    }
  }
}

/// Provider para el servicio de distribución de ganancias
final earningsDistributionServiceProvider =
    Provider<EarningsDistributionService>((ref) {
  return EarningsDistributionService(FirebaseFirestore.instance);
});

/// Provider para obtener todas las distribuciones
final distributionsProvider =
    FutureProvider<List<EarningsDistribution>>((ref) async {
  return ref.read(earningsDistributionServiceProvider).getAllDistributions();
});

/// Provider para obtener una distribución específica
final distributionProvider =
    FutureProvider.family<EarningsDistribution?, String>(
        (ref, distributionId) async {
  return ref
      .read(earningsDistributionServiceProvider)
      .getDistribution(distributionId);
});

/// Provider para calcular las ganancias estimadas de un usuario
final estimatedEarningsProvider =
    FutureProvider.family<double, UserProfile>((ref, userProfile) async {
  return ref
      .read(earningsDistributionServiceProvider)
      .calculateEstimatedEarnings(userProfile);
});
