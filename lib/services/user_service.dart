import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_data.dart';
import '../models/ecpm_config.dart';
import '../models/global_config.dart';
import '../models/withdrawal_management.dart';

class UserService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Obtiene el perfil del usuario actual y actualiza sus ganancias
  static Future<UserProfile?> getCurrentUserProfile() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('Usuario no autenticado');
      }

      final doc = await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .get();
      if (!doc.exists) {
        return null;
      }

      // Obtener el perfil del usuario
      final userProfile = UserProfile.fromFirestore(doc);

      // Calcular y actualizar las ganancias del usuario solo para anuncios nuevos
      await UserProfile.calculateAndUpdateEarnings(userProfile);

      // Obtener el perfil actualizado
      final updatedDoc = await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .get();
      return UserProfile.fromFirestore(updatedDoc);
    } catch (e) {
      print('Error al obtener perfil de usuario: $e');
      return null;
    }
  }

  /// Actualiza el número de teléfono del usuario
  static Future<bool> updatePhoneNumber(String phoneNumber) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('Usuario no autenticado');
      }

      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .update({
        'phoneNumber': phoneNumber,
        // Ya no reseteamos el estado de verificación al cambiar el número
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('Error al actualizar número de teléfono: $e');
      return false;
    }
  }

  /// Actualiza el estado de verificación del teléfono
  static Future<bool> updatePhoneVerificationStatus(bool isVerified) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('Usuario no autenticado');
      }

      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .update({
        'isPhoneVerified': isVerified,
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('Error al actualizar estado de verificación del teléfono: $e');
      return false;
    }
  }

  /// Actualiza el correo electrónico del usuario
  ///
  /// Este método actualiza tanto el correo en Firebase Auth como en Firestore
  static Future<bool> updateEmail(String newEmail, String password) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('Usuario no autenticado');
      }

      // Reautenticar al usuario para operaciones sensibles
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: password,
      );
      await user.reauthenticateWithCredential(credential);

      // Actualizar email en Firebase Auth
      await user.updateEmail(newEmail);

      // Actualizar email en Firestore
      await _firestore
          .collection('projects/pinataparty/users')
          .doc(user.uid)
          .update({
        'email': newEmail,
        'isEmailVerified':
            false, // Resetear el estado de verificación al cambiar el email
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error al actualizar correo electrónico: $e');
      return false;
    }
  }

  /// Actualiza el estado de verificación del correo electrónico
  static Future<bool> updateEmailVerificationStatus(bool isVerified) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('Usuario no autenticado');
      }

      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .update({
        'isEmailVerified': isVerified,
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('Error al actualizar estado de verificación del correo: $e');
      return false;
    }
  }

  /// Solicita un retiro de ganancias disponibles
  static Future<bool> requestWithdrawal(
      double amount, String method, Map<String, dynamic> details) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('Usuario no autenticado');
      }

      // Obtener perfil de usuario para verificar saldo disponible
      // Primero obtenemos el documento directamente sin recalcular ganancias
      final doc = await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .get();
      if (!doc.exists) {
        throw Exception('Perfil de usuario no encontrado');
      }

      final userProfile = UserProfile.fromFirestore(doc);

      // Asegurarse de que las ganancias estén actualizadas antes de verificar el saldo
      await UserProfile.calculateAndUpdateEarnings(userProfile);

      // Obtener el perfil actualizado después de calcular ganancias
      final updatedDoc = await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .get();
      final updatedProfile = UserProfile.fromFirestore(updatedDoc);

      // Verificar que el usuario tenga saldo suficiente
      if (updatedProfile.availableAdEarnings < amount) {
        throw Exception('Saldo insuficiente para realizar el retiro');
      }

      // Verificar que el usuario tenga email verificado
      if (!updatedProfile.isEmailVerified) {
        throw Exception('El correo electrónico no está verificado');
      }

      // Ya no requerimos que el teléfono esté verificado

      // Usar el servicio de retiros para crear la solicitud
      final withdrawalService = WithdrawalService(_firestore);
      final withdrawalId = await withdrawalService.requestWithdrawal(
        userId: userId,
        userEmail: updatedProfile.email,
        userCountry: updatedProfile.country,
        userPhone: updatedProfile.phoneNumber,
        amount: amount,
        type: method,
        details: details,
      );

      return withdrawalId != null;
    } catch (e) {
      print('Error al solicitar retiro: $e');
      return false;
    }
  }

  /// Obtiene las solicitudes de retiro del usuario actual
  static Future<List<Map<String, dynamic>>> getUserWithdrawals() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('Usuario no autenticado');
      }

      final querySnapshot = await _firestore
          .collection('projects/pinataparty/withdrawals')
          .where('userId', isEqualTo: userId)
          .orderBy('requestDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data(),
                'requestDate':
                    (doc.data()['requestDate'] as Timestamp).toDate(),
                'processedDate': doc.data()['processedDate'] != null
                    ? (doc.data()['processedDate'] as Timestamp).toDate()
                    : null,
              })
          .toList();
    } catch (e) {
      print('Error al obtener solicitudes de retiro: $e');
      return [];
    }
  }

  // Este método ha sido reemplazado por UserProfile.calculateAndUpdateEarnings
  // que solo calcula las ganancias para los anuncios nuevos, evitando recalcular
  // repetidamente cuando el usuario realiza retiros.
  @Deprecated('Use UserProfile.calculateAndUpdateEarnings instead')
  static Future<void> calculateAndUpdateEarnings(
      UserProfile userProfile) async {
    await UserProfile.calculateAndUpdateEarnings(userProfile);
  }
}
