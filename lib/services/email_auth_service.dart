import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class EmailAuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Envía un correo de verificación al usuario actual
  static Future<bool> sendVerificationEmail() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('Usuario no autenticado');
      }

      await user.sendEmailVerification();
      return true;
    } catch (e) {
      print('Error al enviar correo de verificación: $e');
      return false;
    }
  }

  /// Verifica si el correo electrónico del usuario está verificado
  static Future<bool> checkEmailVerified() async {
    try {
      // Recargar el usuario para obtener el estado actualizado
      await _auth.currentUser?.reload();
      final user = _auth.currentUser;

      if (user == null) {
        throw Exception('Usuario no autenticado');
      }

      final isVerified = user.emailVerified;

      // Si está verificado, actualizar en Firestore
      if (isVerified) {
        await _updateEmailVerificationStatus(true);
      }

      return isVerified;
    } catch (e) {
      print('Error al verificar correo electrónico: $e');
      return false;
    }
  }

  /// Actualiza el estado de verificación del correo en Firestore
  static Future<bool> _updateEmailVerificationStatus(bool isVerified) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('Usuario no autenticado');
      }

      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .update({
        'isEmailVerified': isVerified,
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error al actualizar estado de verificación de correo: $e');
      return false;
    }
  }
}
