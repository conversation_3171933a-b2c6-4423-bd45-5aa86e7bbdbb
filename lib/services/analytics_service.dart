import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/analytics_data.dart';
import '../models/ad_impression.dart';
import '../models/ecpm_config.dart';
import '../models/user_management.dart';
import 'package:flutter/foundation.dart';
import '../models/user_analytics_data.dart';
import 'firebase_service.dart';
import 'cache_service.dart';
import 'dart:math';

class AnalyticsService {
  final FirebaseFirestore _firestore;

  AnalyticsService({FirebaseFirestore? firestore})
    : _firestore = firestore ?? FirebaseFirestore.instance;

  // Get analytics data for a specific date range
  Future<AnalyticsData> getAnalyticsData({
    required DateTime startDate,
    required DateTime endDate,
    String? userId,
  }) async {
    try {
      final query = _firestore
          .collection('projects/pinataparty/adAnalytics/impressions/records')
          .where('timestamp', isGreaterThanOrEqualTo: startDate)
          .where('timestamp', isLessThanOrEqualTo: endDate);

      if (userId != null) {
        query.where('userId', isEqualTo: userId);
      }

      final snapshot = await query.get();

      int totalImpressions = 0;
      int completedImpressions = 0;
      Map<String, int> impressionsByCountry = {};
      Map<String, int> impressionsByAdType = {};
      Map<String, double> revenueByCountry = {};
      double totalRevenue = 0;

      for (var doc in snapshot.docs) {
        final impression = AdImpression.fromFirestore(doc, null);

        totalImpressions++;
        if (impression.completed) completedImpressions++;

        // Update country stats
        impressionsByCountry[impression.country] =
          (impressionsByCountry[impression.country] ?? 0) + 1;

        // Update ad type stats
        impressionsByAdType[impression.adType] =
          (impressionsByAdType[impression.adType] ?? 0) + 1;

        // Calculate revenue based on eCPM config
        final ecpmConfig = await getECPMConfig(impression.adNetwork, impression.adType);
        final rate = ecpmConfig.getRateForCountry(impression.country);
        final impressionRevenue = rate / 1000; // Convert eCPM to per-impression revenue

        revenueByCountry[impression.country] =
          (revenueByCountry[impression.country] ?? 0) + impressionRevenue;
        totalRevenue += impressionRevenue;
      }

      return AnalyticsData(
        totalImpressions: totalImpressions,
        completedImpressions: completedImpressions,
        impressionsByCountry: impressionsByCountry,
        impressionsByAdType: impressionsByAdType,
        revenueByCountry: revenueByCountry,
        totalRevenue: totalRevenue,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      print('Error fetching analytics data: $e');
      return AnalyticsData.empty();
    }
  }

  // Get eCPM configuration for a specific ad network and type
  Future<ECPMConfig> getECPMConfig(String adNetwork, String adType) async {
    try {
      final doc = await _firestore
          .collection('projects/pinataparty/adAnalytics/config/eCPM')
          .doc('$adNetwork-$adType')
          .get();

      if (doc.exists) {
        return ECPMConfig.fromFirestore(doc, null);
      } else {
        return ECPMConfig.defaultConfig(adNetwork, adType);
      }
    } catch (e) {
      print('Error fetching eCPM config: $e');
      return ECPMConfig.defaultConfig(adNetwork, adType);
    }
  }

  // Add a new ad impression
  Future<void> addAdImpression(AdImpression impression) async {
    try {
      await _firestore
          .collection('projects/pinataparty/adAnalytics/impressions/records')
          .add(impression.toFirestore());
    } catch (e) {
      print('Error adding ad impression: $e');
      throw e;
    }
  }

  // Update eCPM configuration
  Future<void> updateECPMConfig(ECPMConfig config) async {
    try {
      await _firestore
          .collection('projects/pinataparty/adAnalytics/config/eCPM')
          .doc('${config.adNetwork}-${config.adType}')
          .set(config.toFirestore());
    } catch (e) {
      print('Error updating eCPM config: $e');
      throw e;
    }
  }

  // Obtener datos globales para el dashboard de administrador
  static Future<AnalyticsData> fetchGlobalAnalyticsData({
    required DateTime startDate,
    required DateTime endDate,
    required Function(int current, int total) onProgress,
  }) async {
    try {
      // Intentar obtener desde caché primero
      final cachedData = await CacheService.getCachedAnalytics();
      if (cachedData != null) {
        print('Datos encontrados en caché');
        try {
          final cached = AnalyticsData.fromJson(cachedData);

          // Verificar si las fechas coinciden con el rango solicitado
          // Comparamos solo la fecha (año, mes, día) sin la hora
          final cachedStartDate = DateTime(cached.startDate.year, cached.startDate.month, cached.startDate.day);
          final cachedEndDate = DateTime(cached.endDate.year, cached.endDate.month, cached.endDate.day);
          final requestStartDate = DateTime(startDate.year, startDate.month, startDate.day);
          final requestEndDate = DateTime(endDate.year, endDate.month, endDate.day);

          if (cachedStartDate.isAtSameMomentAs(requestStartDate) &&
              cachedEndDate.isAtSameMomentAs(requestEndDate)) {
            print('Usando datos de caché para el rango de fechas solicitado');
            onProgress(100, 100);
            return cached;
          } else {
            print('Fechas en caché no coinciden: Cache[$cachedStartDate-$cachedEndDate] vs Request[$requestStartDate-$requestEndDate]');
          }
        } catch (cacheError) {
          print('Error al procesar datos en caché: $cacheError');
          // Continuar con la obtención de datos desde Firebase
        }
      } else {
        print('No se encontraron datos en caché');
      }

      // Si no está en caché o las fechas no coinciden, obtener de Firebase
      print('Obteniendo datos desde Firebase para el rango: $startDate - $endDate');
      final data = await FirebaseService.getAnalyticsData(
        startDate: startDate,
        endDate: endDate,
        userId: null, // null indica que queremos datos globales
        onProgress: (current, total) {
          onProgress(current, total);
        },
      );

      // Guardar en caché
      print('Guardando datos en caché');
      final jsonData = data.toJson();
      await CacheService.cacheAnalyticsData(jsonData);

      return data;
    } catch (e) {
      print('Error al obtener datos globales: $e');
      throw Exception('Error al obtener datos globales: $e');
    }
  }

  // Obtener datos específicos del usuario
  static Future<UserAnalyticsData> fetchUserAnalyticsData({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
    String? networkFilter,
    required Function(int current, int total) onProgress,
  }) async {
    try {
      // Intentar obtener desde caché primero
      final cachedData = await CacheService.getCachedUserAnalytics(userId);
      if (cachedData != null) {
        final cached = UserAnalyticsData.fromJson(cachedData);
        // Verificar si los datos en caché son válidos para el filtro actual
        if (true) { // TODO: Implementar validación de caché para filtros de usuario
          onProgress(100, 100);
          return cached;
        }
      }

      // Obtener el perfil del usuario
      final userProfile = await FirebaseService.getUserProfile(userId);
      if (userProfile == null) {
        throw Exception('Perfil de usuario no encontrado');
      }

      // Obtener impresiones del usuario
      final userImpressions = await FirebaseService.getUserImpressions(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
        adNetwork: networkFilter,
        onProgress: (current, total) {
          onProgress(current, total);
        },
      );

      // Construir el objeto de datos del usuario
      final userData = UserAnalyticsData(
        userImpressions: userImpressions.length,
        userEarnings: userProfile.adEarnings,
        availableEarnings: userProfile.availableAdEarnings,
        userEmail: userProfile.email,
        userCountry: userProfile.country,
        userPhone: userProfile.phoneNumber,
        recentImpressions: userImpressions.take(10).toList(), // Mostrar solo las 10 más recientes
      );

      // Guardar en caché
      await CacheService.cacheUserAnalytics(userId, userData.toJson());

      return userData;
    } catch (e) {
      throw Exception('Error al obtener datos del usuario: $e');
    }
  }

  // Filtrar datos de analytics según los criterios dados
  static AnalyticsData filterAnalyticsData(
    AnalyticsData data, {
    String? network,
    String? country,
  }) {
    // Si no hay filtros, devolver los datos sin modificar
    if (network == null && country == null) {
      return data;
    }

    // Aplicar filtros
    try {
      final filteredData = AnalyticsData(
        totalImpressions: data.totalImpressions,
        completedImpressions: data.completedImpressions,
        impressionsByCountry: country != null
            ? {country: data.impressionsByCountry[country] ?? 0}
            : data.impressionsByCountry,
        impressionsByAdType: network != null
            ? {network: data.impressionsByAdType[network] ?? 0}
            : data.impressionsByAdType,
        revenueByCountry: country != null
            ? {country: data.revenueByCountry[country] ?? 0}
            : data.revenueByCountry,
        totalRevenue: data.totalRevenue,
        startDate: data.startDate,
        endDate: data.endDate,
      );

      return filteredData;
    } catch (e) {
      // Si hay error al aplicar filtros, devolver datos originales
      print('Error al filtrar datos: $e');
      return data;
    }
  }

  // Nota: La generación de datos de prueba se ha movido a TestDataService
  // para centralizar todos los métodos de generación de datos de prueba
}