import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/analytics_data.dart';
import '../models/ad_impression.dart';
import '../models/user_data.dart';
import '../models/withdrawal_management.dart';
import '../models/user_management.dart';
import 'admin_service.dart';
import 'dart:math' as math;

class FirebaseService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static bool _mockDataInitialized = false;
  static const int _pageSize = 1000;

  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  static User? get currentUser => _auth.currentUser;

  static String? get currentUserId => _auth.currentUser?.uid;

  static bool get isUserAdmin {
    final user = _auth.currentUser;
    return user != null && user.email != null && user.email!.contains('admin');
  }

  static Future<UserCredential> signIn(String email, String password) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      throw Exception('Error al iniciar sesión: $e');
    }
  }

  static Future<User?> createTestUser(String email, String password) async {
    try {
      // Crear usuario en Authentication
      final UserCredential userCredential =
          await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Crear perfil en Firestore (colección de prueba)
      await _firestore
          .collection('projects/pinataparty/users_v1')
          .doc(userCredential.user!.uid)
          .set({
        'email': email,
        'createAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
        'lastUpdateAt': FieldValue.serverTimestamp(),
        'adEarnings': 0.0,
        'availableAdEarnings': 0.0,
        'country': 'MX', // País por defecto
        'isActive': true,
        'completedRewardAds': 0,
        'referralCount': 0,
        'totalReward': 0.0,
      });

      return userCredential.user;
    } catch (e) {
      print('Error creating test user: $e');
      return null;
    }
  }

  static Future<void> signOut() async {
    await _auth.signOut();
  }

  static Future<AnalyticsData> getAnalyticsData({
    required DateTime startDate,
    required DateTime endDate,
    String? userId,
    required Function(int current, int total) onProgress,
  }) async {
    try {
      print(
          'Obteniendo datos de analytics desde Firebase para el rango: $startDate - $endDate');
      onProgress(0, 100);

      // Consultar impresiones en el rango de fechas
      Query query = _firestore
          .collection('projects/pinataparty/adAnalytics/impressions/records')
          .where('timestamp',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate));

      // Filtrar por usuario si se especifica
      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }

      onProgress(10, 100);
      final snapshot = await query.get();
      print(
          'Consulta completada. Documentos encontrados: ${snapshot.docs.length}');

      // Procesar resultados
      int totalImpressions = snapshot.docs.length;
      int completedImpressions = 0;
      Map<String, int> impressionsByCountry = {};
      Map<String, int> impressionsByAdType = {};
      Map<String, double> revenueByCountry = {};
      double totalRevenue = 0.0;

      onProgress(30, 100);

      // Procesar cada documento
      int processedDocs = 0;
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final country = data['country'] as String? ?? 'unknown';
        final adNetwork = data['adNetwork'] as String? ?? 'unknown';
        final completed = data['completed'] as bool? ?? false;

        // Actualizar contadores
        if (completed) {
          completedImpressions++;
        }

        // Actualizar impresiones por país
        impressionsByCountry[country] =
            (impressionsByCountry[country] ?? 0) + 1;

        // Actualizar impresiones por red
        impressionsByAdType[adNetwork] =
            (impressionsByAdType[adNetwork] ?? 0) + 1;

        // Calcular ingresos basados en eCPM
        // Obtener configuración de eCPM para esta red y tipo de anuncio
        final ecpmDoc = await _firestore
            .collection('projects/pinataparty/adAnalytics/config/eCPM')
            .doc('$adNetwork-reward')
            .get();

        if (ecpmDoc.exists && completed) {
          final ecpmData = ecpmDoc.data()!;
          final countryRates =
              ecpmData['countryRates'] as Map<String, dynamic>? ?? {};
          final defaultRate =
              (ecpmData['defaultRate'] as num?)?.toDouble() ?? 2.0;

          // Obtener tarifa para el país o usar la predeterminada
          final rate = countryRates[country] != null
              ? (countryRates[country] as num).toDouble()
              : defaultRate;

          // Calcular ingresos por impresión (eCPM / 1000)
          final revenue = rate / 1000;

          // Actualizar ingresos por país
          revenueByCountry[country] =
              (revenueByCountry[country] ?? 0.0) + revenue;
          totalRevenue += revenue;
        }

        // Actualizar progreso
        processedDocs++;
        if (processedDocs % 10 == 0) {
          onProgress(30 + (processedDocs * 60 / totalImpressions).round(), 100);
        }
      }

      onProgress(90, 100);

      // Crear objeto AnalyticsData con los resultados
      final analyticsData = AnalyticsData(
        totalImpressions: totalImpressions,
        completedImpressions: completedImpressions,
        impressionsByCountry: impressionsByCountry,
        impressionsByAdType: impressionsByAdType,
        revenueByCountry: revenueByCountry,
        totalRevenue: double.parse(totalRevenue.toStringAsFixed(2)),
        startDate: startDate,
        endDate: endDate,
      );

      onProgress(100, 100);
      print('Datos de analytics procesados correctamente');
      return analyticsData;
    } catch (e) {
      print('Error al obtener datos de analytics: $e');
      throw Exception('Error al obtener datos de analytics: $e');
    }
  }

  static Future<UserProfile?> getUserProfile(String userId) async {
    try {
      print('Obteniendo perfil de usuario desde Firebase: $userId');
      final doc = await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .get();

      if (!doc.exists) {
        print('Perfil de usuario no encontrado: $userId');
        return null;
      }

      final data = doc.data()!;

      return UserProfile(
        id: userId,
        email: data['email'] as String? ?? '<EMAIL>',
        phoneNumber: data['phoneNumber'] as String?,
        createAt: (data['createAt'] as Timestamp?)?.toDate() ??
            DateTime.now().subtract(const Duration(days: 90)),
        country: data['country'] as String? ?? 'unknown',
        adEarnings: (data['adEarnings'] as num?)?.toDouble() ?? 0.0,
        availableAdEarnings:
            (data['availableAdEarnings'] as num?)?.toDouble() ?? 0.0,
        isActive: data['isActive'] as bool? ?? true,
        completedRewardAds: (data['completedRewardAds'] as num?)?.toInt() ?? 0,
      );
    } catch (e) {
      print('Error al obtener perfil de usuario: $e');
      throw Exception('Error al obtener perfil de usuario: $e');
    }
  }

  static Future<List<AdImpression>> getUserImpressions({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
    String? adNetwork,
    required Function(int current, int total) onProgress,
  }) async {
    try {
      print('Obteniendo impresiones de usuario desde Firebase: $userId');
      onProgress(0, 100);

      // Construir la consulta base
      Query query = _firestore
          .collection('projects/pinataparty/adAnalytics/impressions/records')
          .where('userId', isEqualTo: userId)
          .where('timestamp',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate));

      // Aplicar filtro de red si se especifica
      if (adNetwork != null) {
        query = query.where('adNetwork', isEqualTo: adNetwork);
      }

      // Ordenar por fecha descendente
      query = query.orderBy('timestamp', descending: true);

      onProgress(20, 100);
      final snapshot = await query.get();
      print(
          'Consulta completada. Impresiones encontradas: ${snapshot.docs.length}');

      // Convertir documentos a objetos AdImpression
      final impressions = <AdImpression>[];
      int processedDocs = 0;

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        try {
          impressions.add(AdImpression(
            id: doc.id,
            userId: data['userId'] as String,
            timestamp: (data['timestamp'] as Timestamp).toDate(),
            adNetwork: data['adNetwork'] as String? ?? 'unknown',
            adType: data['adType'] as String? ?? 'reward',
            completed: data['completed'] as bool? ?? false,
            country: data['country'] as String? ?? 'unknown',
            rewardAmount: data['completed'] == true
                ? (data['rewardAmount'] as num?)?.toInt()
                : null,
            revenue: (data['revenue'] as num?)?.toDouble() ?? 0.0,
            appVersion: data['appVersion'] as String? ?? '1.0.0',
          ));
        } catch (e) {
          print('Error al procesar impresión ${doc.id}: $e');
        }

        // Actualizar progreso
        processedDocs++;
        if (processedDocs % 10 == 0) {
          onProgress(
              20 + (processedDocs * 70 / snapshot.docs.length).round(), 100);
        }
      }

      onProgress(90, 100);

      // Ordenar por fecha descendente (más reciente primero)
      impressions.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      onProgress(100, 100);
      print(
          'Procesadas ${impressions.length} impresiones para usuario $userId');
      return impressions;
    } catch (e) {
      print('Error al obtener impresiones de usuario: $e');
      throw Exception('Error al obtener impresiones de usuario: $e');
    }
  }

  static Future<void> initializeAnalyticsStructure() async {
    try {
      print('Iniciando inicialización de estructura de analytics');

      // Check if the analytics structure exists
      final docRef = _firestore.doc('projects/pinataparty');
      final docSnapshot = await docRef.get();

      if (!docSnapshot.exists) {
        print('Creando estructura base de analytics...');
        // Create the base structure
        await docRef.set({
          'created': FieldValue.serverTimestamp(),
        });

        // Create adAnalytics structure
        final analyticsRef = docRef.collection('adAnalytics');

        // Create impressions collection
        await analyticsRef.doc('impressions').set({
          'created': FieldValue.serverTimestamp(),
        });

        // Create config/eCPM structure
        final configRef = analyticsRef.doc('config');
        await configRef.set({
          'created': FieldValue.serverTimestamp(),
        });

        final ecpmRef = configRef.collection('eCPM').doc('networks');
        await ecpmRef.set({
          'created': FieldValue.serverTimestamp(),
          'configurations': {},
        });
        print('Estructura base creada correctamente');

        // Nota: No inicializamos datos de prueba aquí, eso debe hacerse desde TestDataService
      } else {
        print('La estructura base ya existe');
      }
    } catch (e) {
      print('Error inicializando estructura de analytics: $e');
      throw e;
    }
  }
}
