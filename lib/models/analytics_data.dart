import 'package:cloud_firestore/cloud_firestore.dart';

class AnalyticsData {
  final int totalImpressions;
  final int completedImpressions;
  final Map<String, int> impressionsByCountry;
  final Map<String, int> impressionsByAdType;
  final Map<String, double> revenueByCountry;
  final double totalRevenue;
  final DateTime startDate;
  final DateTime endDate;

  // Propiedades adicionales para dashboards
  final Map<String, NetworkStats> networkStats;
  final Map<String, CountryStats> countryStats;
  final double averageEcpm;

  double get completionRate =>
    totalImpressions > 0 ? completedImpressions / totalImpressions : 0.0;

  AnalyticsData({
    required this.totalImpressions,
    required this.completedImpressions,
    required this.impressionsByCountry,
    required this.impressionsByAdType,
    required this.revenueByCountry,
    required this.totalRevenue,
    required this.startDate,
    required this.endDate,
    Map<String, NetworkStats>? networkStats,
    Map<String, CountryStats>? countryStats,
    double? averageEcpm,
  }) :
    this.networkStats = networkStats ?? _generateNetworkStats(impressionsByAdType, revenueByCountry, totalImpressions),
    this.countryStats = countryStats ?? _generateCountryStats(impressionsByCountry, revenueByCountry, totalImpressions),
    this.averageEcpm = averageEcpm ?? (totalImpressions > 0 ? totalRevenue / totalImpressions * 1000 : 0.0);

  static Map<String, NetworkStats> _generateNetworkStats(
    Map<String, int> impressionsByAdType,
    Map<String, double> revenueByCountry,
    int totalImpressions
  ) {
    final Map<String, NetworkStats> result = {};

    // Estimar ingresos por red
    final Map<String, double> revenueByNetwork = {};
    double totalRev = revenueByCountry.values.fold(0, (a, b) => a + b);

    // Distribuir ingresos por red proporcional a impresiones
    for (var network in impressionsByAdType.keys) {
      final networkPct = totalImpressions > 0
          ? impressionsByAdType[network]! / totalImpressions
          : 0.0;
      revenueByNetwork[network] = totalRev * networkPct;
    }

    // Crear objeto NetworkStats para cada red
    for (var network in impressionsByAdType.keys) {
      final impressions = impressionsByAdType[network] ?? 0;
      final revenue = revenueByNetwork[network] ?? 0.0;
      final ecpm = impressions > 0 ? revenue / impressions * 1000 : 0.0;

      result[network] = NetworkStats(
        impressions: impressions,
        revenue: revenue,
        ecpm: ecpm,
      );
    }

    return result;
  }

  static Map<String, CountryStats> _generateCountryStats(
    Map<String, int> impressionsByCountry,
    Map<String, double> revenueByCountry,
    int totalImpressions
  ) {
    final Map<String, CountryStats> result = {};

    for (var country in impressionsByCountry.keys) {
      final impressions = impressionsByCountry[country] ?? 0;
      final revenue = revenueByCountry[country] ?? 0.0;
      final ecpm = impressions > 0 ? revenue / impressions * 1000 : 0.0;

      result[country] = CountryStats(
        impressions: impressions,
        revenue: revenue,
        ecpm: ecpm,
      );
    }

    return result;
  }

  double getRevenueForCountry(String country) =>
    revenueByCountry[country] ?? 0.0;

  int getImpressionsForCountry(String country) =>
    impressionsByCountry[country] ?? 0;

  int getImpressionsForAdType(String adType) =>
    impressionsByAdType[adType] ?? 0;

  Map<String, dynamic> toFirestore() {
    return {
      'totalImpressions': totalImpressions,
      'completedImpressions': completedImpressions,
      'impressionsByCountry': impressionsByCountry,
      'impressionsByAdType': impressionsByAdType,
      'revenueByCountry': revenueByCountry,
      'totalRevenue': totalRevenue,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
    };
  }

  factory AnalyticsData.fromFirestore(
    DocumentSnapshot snapshot,
    SnapshotOptions? options
  ) {
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;

    return AnalyticsData(
      totalImpressions: data['totalImpressions'] ?? 0,
      completedImpressions: data['completedImpressions'] ?? 0,
      impressionsByCountry: Map<String, int>.from(data['impressionsByCountry'] ?? {}),
      impressionsByAdType: Map<String, int>.from(data['impressionsByAdType'] ?? {}),
      revenueByCountry: Map<String, double>.from(data['revenueByCountry'] ?? {}),
      totalRevenue: data['totalRevenue'] ?? 0.0,
      startDate: (data['startDate'] as Timestamp?)?.toDate() ??
          DateTime.now().subtract(const Duration(days: 7)),
      endDate: (data['endDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    // Convertir los mapas a formatos serializables
    final Map<String, dynamic> impressionsByCountryJson = {};
    impressionsByCountry.forEach((key, value) {
      impressionsByCountryJson[key] = value;
    });

    final Map<String, dynamic> impressionsByAdTypeJson = {};
    impressionsByAdType.forEach((key, value) {
      impressionsByAdTypeJson[key] = value;
    });

    final Map<String, dynamic> revenueByCountryJson = {};
    revenueByCountry.forEach((key, value) {
      revenueByCountryJson[key] = value;
    });

    return {
      'totalImpressions': totalImpressions,
      'completedImpressions': completedImpressions,
      'impressionsByCountry': impressionsByCountryJson,
      'impressionsByAdType': impressionsByAdTypeJson,
      'revenueByCountry': revenueByCountryJson,
      'totalRevenue': totalRevenue,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      // Incluir valores calculados para evitar recálculos
      'averageEcpm': averageEcpm,
    };
  }

  factory AnalyticsData.fromJson(Map<String, dynamic> json) {
    try {
      // Convertir los mapas a los tipos correctos con manejo de errores
      final Map<String, int> impressionsByCountry = {};
      (json['impressionsByCountry'] as Map).forEach((key, value) {
        impressionsByCountry[key.toString()] = (value is int) ? value : int.parse(value.toString());
      });

      final Map<String, int> impressionsByAdType = {};
      (json['impressionsByAdType'] as Map).forEach((key, value) {
        impressionsByAdType[key.toString()] = (value is int) ? value : int.parse(value.toString());
      });

      final Map<String, double> revenueByCountry = {};
      (json['revenueByCountry'] as Map).forEach((key, value) {
        revenueByCountry[key.toString()] = (value is double) ? value : double.parse(value.toString());
      });

      return AnalyticsData(
        totalImpressions: json['totalImpressions'] as int,
        completedImpressions: json['completedImpressions'] as int,
        impressionsByCountry: impressionsByCountry,
        impressionsByAdType: impressionsByAdType,
        revenueByCountry: revenueByCountry,
        totalRevenue: (json['totalRevenue'] is double) ?
                      json['totalRevenue'] as double :
                      double.parse(json['totalRevenue'].toString()),
        startDate: DateTime.parse(json['startDate'] as String),
        endDate: DateTime.parse(json['endDate'] as String),
        // Usar el valor precalculado si está disponible
        averageEcpm: json['averageEcpm'] != null ?
                    (json['averageEcpm'] is double) ?
                    json['averageEcpm'] as double :
                    double.parse(json['averageEcpm'].toString()) :
                    null,
      );
    } catch (e) {
      print('Error al deserializar AnalyticsData: $e');
      print('JSON recibido: $json');
      // Devolver un objeto vacío en caso de error
      return AnalyticsData.empty();
    }
  }

  factory AnalyticsData.empty() {
    return AnalyticsData(
      totalImpressions: 0,
      completedImpressions: 0,
      impressionsByCountry: {},
      impressionsByAdType: {},
      revenueByCountry: {},
      totalRevenue: 0.0,
      startDate: DateTime.now().subtract(const Duration(days: 7)),
      endDate: DateTime.now(),
    );
  }

  // Método para crear una copia con algunas propiedades modificadas
  AnalyticsData copyWith({
    int? totalImpressions,
    int? completedImpressions,
    Map<String, int>? impressionsByCountry,
    Map<String, int>? impressionsByAdType,
    Map<String, double>? revenueByCountry,
    double? totalRevenue,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return AnalyticsData(
      totalImpressions: totalImpressions ?? this.totalImpressions,
      completedImpressions: completedImpressions ?? this.completedImpressions,
      impressionsByCountry: impressionsByCountry ?? Map<String, int>.from(this.impressionsByCountry),
      impressionsByAdType: impressionsByAdType ?? Map<String, int>.from(this.impressionsByAdType),
      revenueByCountry: revenueByCountry ?? Map<String, double>.from(this.revenueByCountry),
      totalRevenue: totalRevenue ?? this.totalRevenue,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
}

class NetworkStats {
  final int impressions;
  final double revenue;
  final double ecpm;

  NetworkStats({
    required this.impressions,
    required this.revenue,
    required this.ecpm,
  });
}

class CountryStats {
  final int impressions;
  final double revenue;
  final double ecpm;

  CountryStats({
    required this.impressions,
    required this.revenue,
    required this.ecpm,
  });
}