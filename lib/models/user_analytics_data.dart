import '../models/ad_impression.dart';

class UserAnalyticsData {
  final int userImpressions;
  final double userEarnings;
  final double availableEarnings;
  final String userEmail;
  final String userCountry;
  final String? userPhone;
  final List<AdImpression> recentImpressions;

  UserAnalyticsData({
    required this.userImpressions,
    required this.userEarnings,
    required this.availableEarnings,
    required this.userEmail,
    required this.userCountry,
    this.userPhone,
    required this.recentImpressions,
  });

  factory UserAnalyticsData.empty() {
    return UserAnalyticsData(
      userImpressions: 0,
      userEarnings: 0.0,
      availableEarnings: 0.0,
      userEmail: '',
      userCountry: '',
      recentImpressions: [],
    );
  }

  factory UserAnalyticsData.fromJson(Map<String, dynamic> json) {
    return UserAnalyticsData(
      userImpressions: json['userImpressions'] ?? 0,
      userEarnings: (json['userEarnings'] ?? 0.0).toDouble(),
      availableEarnings: (json['availableEarnings'] ?? 0.0).toDouble(),
      userEmail: json['userEmail'] ?? '',
      userCountry: json['userCountry'] ?? '',
      userPhone: json['userPhone'],
      recentImpressions: (json['recentImpressions'] as List<dynamic>?)
          ?.map((impression) => AdImpression.fromJson(impression))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userImpressions': userImpressions,
      'userEarnings': userEarnings,
      'availableEarnings': availableEarnings,
      'userEmail': userEmail,
      'userCountry': userCountry,
      'userPhone': userPhone,
      'recentImpressions': recentImpressions.map((impression) => impression.toJson()).toList(),
    };
  }
} 