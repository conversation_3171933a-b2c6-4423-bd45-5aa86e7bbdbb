import 'package:cloud_firestore/cloud_firestore.dart';

/// Modelo para gestionar solicitudes de retiro
///
/// Se almacena en 'projects/pinataparty/withdrawals/{withdrawalId}'
class WithdrawalManagement {
  final String id;
  final String userId;
  final String userEmail;
  final String userCountry;
  final String? userPhone;
  final double amount;
  final DateTime requestDate;
  final String status; // pending/approved/rejected
  final String type; // mobile_recharge/gift_card
  final Map<String, dynamic> details;
  final DateTime? processedDate;
  final String? processedBy;
  final String? rejectionReason;

  WithdrawalManagement({
    required this.id,
    required this.userId,
    required this.userEmail,
    required this.userCountry,
    this.userPhone,
    required this.amount,
    required this.requestDate,
    required this.status,
    required this.type,
    required this.details,
    this.processedDate,
    this.processedBy,
    this.rejectionReason,
  });

  factory WithdrawalManagement.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data()!;
    return WithdrawalManagement(
      id: snapshot.id,
      userId: data['userId'] as String,
      userEmail: data['userEmail'] as String,
      userCountry: data['userCountry'] as String,
      userPhone: data['userPhone'] as String?,
      amount: (data['amount'] as num).toDouble(),
      requestDate: (data['requestDate'] as Timestamp).toDate(),
      status: data['status'] as String,
      type: data['type'] as String,
      details: data['details'] as Map<String, dynamic>,
      processedDate: data['processedDate'] != null
          ? (data['processedDate'] as Timestamp).toDate()
          : null,
      processedBy: data['processedBy'] as String?,
      rejectionReason: data['rejectionReason'] as String?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userEmail': userEmail,
      'userCountry': userCountry,
      if (userPhone != null) 'userPhone': userPhone,
      'amount': amount,
      'requestDate': Timestamp.fromDate(requestDate),
      'status': status,
      'type': type,
      'details': details,
      if (processedDate != null)
        'processedDate': Timestamp.fromDate(processedDate!),
      if (processedBy != null) 'processedBy': processedBy,
      if (rejectionReason != null) 'rejectionReason': rejectionReason,
    };
  }

  WithdrawalManagement copyWith({
    String? id,
    String? userId,
    String? userEmail,
    String? userCountry,
    String? userPhone,
    double? amount,
    DateTime? requestDate,
    String? status,
    String? type,
    Map<String, dynamic>? details,
    DateTime? processedDate,
    String? processedBy,
    String? rejectionReason,
  }) {
    return WithdrawalManagement(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userEmail: userEmail ?? this.userEmail,
      userCountry: userCountry ?? this.userCountry,
      userPhone: userPhone ?? this.userPhone,
      amount: amount ?? this.amount,
      requestDate: requestDate ?? this.requestDate,
      status: status ?? this.status,
      type: type ?? this.type,
      details: details ?? Map<String, dynamic>.from(this.details),
      processedDate: processedDate ?? this.processedDate,
      processedBy: processedBy ?? this.processedBy,
      rejectionReason: rejectionReason ?? this.rejectionReason,
    );
  }

  /// Ruta de la colección donde se almacenan los retiros
  static String collectionPath() {
    return 'projects/pinataparty/withdrawals';
  }
}

/// Configuración del sistema de retiros
///
/// Se almacena en 'projects/pinataparty/adminSettings/withdrawalSettings'
class WithdrawalSettings {
  final double minimumAmount;
  final double withdrawalPercentage;
  final int processingTimeHours;
  final List<String> availableTypes;
  final Map<String, List<String>> typeOptions;

  WithdrawalSettings({
    required this.minimumAmount,
    required this.withdrawalPercentage,
    required this.processingTimeHours,
    required this.availableTypes,
    required this.typeOptions,
  });

  factory WithdrawalSettings.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data()!;
    return WithdrawalSettings(
      minimumAmount: (data['minimumAmount'] as num).toDouble(),
      withdrawalPercentage: (data['withdrawalPercentage'] as num).toDouble(),
      processingTimeHours: data['processingTimeHours'] as int,
      availableTypes: List<String>.from(data['availableTypes']),
      typeOptions: (data['typeOptions'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(key, List<String>.from(value)),
      ),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'minimumAmount': minimumAmount,
      'withdrawalPercentage': withdrawalPercentage,
      'processingTimeHours': processingTimeHours,
      'availableTypes': availableTypes,
      'typeOptions': typeOptions,
    };
  }

  factory WithdrawalSettings.defaultSettings() {
    return WithdrawalSettings(
      minimumAmount: 10.0,
      withdrawalPercentage: 0.5,
      processingTimeHours: 48,
      availableTypes: ['mobile_recharge', 'gift_card'],
      typeOptions: {
        'mobile_recharge': ['Telcel', 'Movistar', 'AT&T'],
        'gift_card': ['Amazon', 'Google Play', 'iTunes'],
      },
    );
  }

  /// Ruta del documento donde se almacena la configuración de retiros
  static String documentPath() {
    return 'projects/pinataparty/adminSettings/withdrawalSettings';
  }
}

/// Registro de acciones administrativas
///
/// Se almacena en 'projects/pinataparty/adminLogs/{logId}'
class AdminLog {
  final String id;
  final String adminId;
  final String adminEmail;
  final String action;
  final DateTime timestamp;
  final Map<String, dynamic> details;

  AdminLog({
    required this.id,
    required this.adminId,
    required this.adminEmail,
    required this.action,
    required this.timestamp,
    required this.details,
  });

  factory AdminLog.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data()!;
    return AdminLog(
      id: snapshot.id,
      adminId: data['adminId'] as String,
      adminEmail: data['adminEmail'] as String,
      action: data['action'] as String,
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      details: data['details'] as Map<String, dynamic>,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'adminId': adminId,
      'adminEmail': adminEmail,
      'action': action,
      'timestamp': Timestamp.fromDate(timestamp),
      'details': details,
    };
  }

  /// Ruta de la colección donde se almacenan los logs administrativos
  static String collectionPath() {
    return 'projects/pinataparty/adminLogs';
  }
}

/// Clase para gestionar retiros en el sistema web
class WithdrawalService {
  final FirebaseFirestore _firestore;

  WithdrawalService(this._firestore);

  /// Solicita un nuevo retiro
  Future<String?> requestWithdrawal({
    required String userId,
    required String userEmail,
    required String userCountry,
    String? userPhone,
    required double amount,
    required String type,
    required Map<String, dynamic> details,
  }) async {
    try {
      // Verificar si el usuario tiene saldo suficiente
      final userDoc = await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .get();
      if (!userDoc.exists) {
        return null;
      }

      final userData = userDoc.data()!;
      final availableAdEarnings =
          (userData['availableAdEarnings'] as num?)?.toDouble() ?? 0.0;

      if (availableAdEarnings < amount) {
        return null;
      }

      // Crear solicitud de retiro
      final withdrawalRef = await _firestore
          .collection(WithdrawalManagement.collectionPath())
          .add({
        'userId': userId,
        'userEmail': userEmail,
        'userCountry': userCountry,
        if (userPhone != null) 'userPhone': userPhone,
        'amount': amount,
        'requestDate': FieldValue.serverTimestamp(),
        'status': 'pending',
        'type': type,
        'details': details,
      });

      // Actualizar saldo disponible del usuario y registrar información del retiro
      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .update({
        'availableAdEarnings': FieldValue.increment(-amount),
        'lastUpdateAt': FieldValue.serverTimestamp(),
        'lastWithdrawalDate': FieldValue.serverTimestamp(),
        'lastWithdrawalAmount': amount,
      });

      return withdrawalRef.id;
    } catch (e) {
      print('Error al solicitar retiro: $e');
      return null;
    }
  }

  /// Obtiene los retiros pendientes de un usuario
  Future<List<WithdrawalManagement>> getUserPendingWithdrawals(
      String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(WithdrawalManagement.collectionPath())
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: 'pending')
          .orderBy('requestDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => WithdrawalManagement.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>, null))
          .toList();
    } catch (e) {
      print('Error al obtener retiros pendientes: $e');
      return [];
    }
  }

  /// Obtiene el historial de retiros de un usuario
  Future<List<WithdrawalManagement>> getUserWithdrawalHistory(
      String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(WithdrawalManagement.collectionPath())
          .where('userId', isEqualTo: userId)
          .where('status', isNotEqualTo: 'pending')
          .orderBy('status')
          .orderBy('requestDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => WithdrawalManagement.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>, null))
          .toList();
    } catch (e) {
      print('Error al obtener historial de retiros: $e');
      return [];
    }
  }

  /// Obtiene la configuración de retiros
  Future<WithdrawalSettings> getWithdrawalSettings() async {
    try {
      final doc = await _firestore.doc(WithdrawalSettings.documentPath()).get();

      if (doc.exists) {
        return WithdrawalSettings.fromFirestore(
            doc as DocumentSnapshot<Map<String, dynamic>>, null);
      } else {
        // Si no existe, crear con valores por defecto
        final defaultSettings = WithdrawalSettings.defaultSettings();
        await _firestore
            .doc(WithdrawalSettings.documentPath())
            .set(defaultSettings.toFirestore());
        return defaultSettings;
      }
    } catch (e) {
      print('Error al obtener configuración de retiros: $e');
      return WithdrawalSettings.defaultSettings();
    }
  }
}
