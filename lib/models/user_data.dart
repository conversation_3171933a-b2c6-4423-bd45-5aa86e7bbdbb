import 'package:cloud_firestore/cloud_firestore.dart';
import 'ad_impression.dart';
import 'ecpm_config.dart';
import 'global_config.dart';

/// Perfil de usuario extendido para el sistema web
///
/// Versión extendida del modelo UserManagement que incluye campos adicionales
/// para el sistema de distribución de ganancias (estimatedEarnings y lastDistributionDate).
/// Este modelo se utiliza principalmente en la interfaz de usuario para mostrar información
/// completa sobre las ganancias del usuario.
class UserProfile {
  // IDENTIFICACIÓN BÁSICA
  final String id; // ID único del usuario
  final String email; // Correo electrónico del usuario
  final String? phoneNumber; // Número de teléfono (opcional)
  final DateTime createAt; // Fecha de registro
  final String country; // País del usuario
  final bool isActive; // Estado del usuario (activo/inactivo)
  final bool isEmailVerified; // Si el correo ha sido verificado
  final bool isPhoneVerified; // Si el teléfono ha sido verificado

  // MÉTRICAS DE ANUNCIOS
  final int completedRewardAds; // Total de anuncios de recompensa completados
  final int
      processedRewardAds; // Anuncios ya procesados para cálculo de ganancias

  // DATOS FINANCIEROS
  final double adEarnings; // Ganancias totales acumuladas a lo largo del tiempo
  final double
      availableAdEarnings; // Ganancias disponibles para retirar (confirmadas por el administrador)

  // CAMPOS ADICIONALES PARA EL SISTEMA DE DISTRIBUCIÓN DE GANANCIAS
  final double
      estimatedEarnings; // Ganancias estimadas pero aún no confirmadas por el administrador
  final DateTime?
      lastDistributionDate; // Fecha de la última distribución de ganancias realizada

  // Sistema de referidos
  final String? referralCode;
  final int referralCount;
  final String? referredBy;
  final double totalReward;
  final double? offerwallEarnings; // Ganancias de Offerwall

  UserProfile({
    required this.id,
    required this.email,
    this.phoneNumber,
    required this.createAt,
    required this.country,
    this.adEarnings = 0.0,
    this.availableAdEarnings = 0.0,
    this.isActive = true,
    this.completedRewardAds = 0,
    this.processedRewardAds = 0,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.estimatedEarnings = 0.0,
    this.lastDistributionDate,
    this.referralCode,
    this.referralCount = 0,
    this.referredBy,
    this.totalReward = 0.0,
    this.offerwallEarnings,
  });

  /// Crea un UserProfile a partir de un documento de Firestore
  factory UserProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserProfile(
      id: doc.id,
      email: data['email'] as String? ?? '<EMAIL>',
      phoneNumber: data['phoneNumber'] as String?,
      // Intentar obtener la fecha de registro de 'createAt' o 'createdAt'
      createAt: data['createAt'] != null
          ? (data['createAt'] as Timestamp).toDate()
          : data['createdAt'] != null
              ? (data['createdAt'] as Timestamp).toDate()
              : DateTime.now(),
      country: data['country'] as String? ?? 'unknown',
      adEarnings: (data['adEarnings'] as num?)?.toDouble() ?? 0.0,
      availableAdEarnings:
          (data['availableAdEarnings'] as num?)?.toDouble() ?? 0.0,
      isActive: data['isActive'] as bool? ?? true,
      completedRewardAds: (data['completedRewardAds'] as num?)?.toInt() ?? 0,
      processedRewardAds: (data['processedRewardAds'] as num?)?.toInt() ?? 0,
      isEmailVerified: data['isEmailVerified'] as bool? ?? false,
      isPhoneVerified: data['isPhoneVerified'] as bool? ?? false,
      estimatedEarnings: (data['estimatedEarnings'] as num?)?.toDouble() ?? 0.0,
      lastDistributionDate: data['lastDistributionDate'] != null
          ? (data['lastDistributionDate'] as Timestamp).toDate()
          : null,
      referralCode: data['referralCode'] as String?,
      referralCount: (data['referralCount'] as num?)?.toInt() ?? 0,
      referredBy: data['referredBy'] as String?,
      totalReward: (data['totalReward'] as num?)?.toDouble() ?? 0.0,
      offerwallEarnings: (data['offerwallEarnings'] as num?)?.toDouble() ?? 0.0,
    );
  }

  /// Convierte el perfil a un mapa para Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      if (phoneNumber != null) 'phoneNumber': phoneNumber,
      'createAt': Timestamp.fromDate(createAt),
      'country': country,
      'adEarnings': adEarnings,
      'availableAdEarnings': availableAdEarnings,
      'isActive': isActive,
      'completedRewardAds': completedRewardAds,
      'processedRewardAds': processedRewardAds,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'estimatedEarnings': estimatedEarnings,
      if (lastDistributionDate != null)
        'lastDistributionDate': Timestamp.fromDate(lastDistributionDate!),
      if (referralCode != null) 'referralCode': referralCode,
      'referralCount': referralCount,
      if (referredBy != null) 'referredBy': referredBy,
      'totalReward': totalReward,
      'offerwallEarnings': offerwallEarnings,
    };
  }

  /// Calcula y actualiza las ganancias estimadas del usuario basado en el contador de anuncios completados
  ///
  /// Este método calcula las ganancias estimadas para los anuncios que aún no han sido procesados
  /// por el administrador en una distribución de ganancias. Estas ganancias son aproximadas
  /// y serán confirmadas en la próxima distribución.
  static Future<void> calculateAndUpdateEarnings(
      UserProfile userProfile) async {
    final FirebaseFirestore _firestore = FirebaseFirestore.instance;

    try {
      // Verificar si hay anuncios nuevos para procesar
      final int pendingAds =
          userProfile.completedRewardAds - userProfile.processedRewardAds;

      if (pendingAds <= 0) {
        print('No hay nuevos anuncios para procesar');
        return;
      }

      print('Procesando $pendingAds anuncios pendientes');

      // Obtener la configuración de eCPM para las tres redes publicitarias
      final adNetworks = ['AdMob', 'Unity', 'Meta'];
      double totalRate = 0.0;
      int networksFound = 0;

      for (final network in adNetworks) {
        final ecpmDoc = await _firestore
            .collection('projects/pinataparty/adAnalytics/config/eCPM')
            .doc('$network-reward')
            .get();

        if (ecpmDoc.exists) {
          final ecpmData = ecpmDoc.data()!;
          final countryRates =
              ecpmData['countryRates'] as Map<String, dynamic>? ?? {};
          final defaultRate =
              (ecpmData['defaultRate'] as num?)?.toDouble() ?? 2.0;

          // Obtener tarifa para el país o usar la predeterminada
          final networkRate = countryRates[userProfile.country] != null
              ? (countryRates[userProfile.country] as num).toDouble()
              : defaultRate;

          totalRate += networkRate;
          networksFound++;
          print('Encontrada configuración eCPM para $network: $networkRate');
        } else {
          print(
              'No se encontró configuración de eCPM para $network, usando valor predeterminado');
          // Usar un valor predeterminado si no se encuentra la configuración
          totalRate += 2.0; // Valor predeterminado
          networksFound++;
        }
      }

      // Si no se encontró ninguna configuración, usar un valor predeterminado
      if (networksFound == 0) {
        print(
            'No se encontró ninguna configuración de eCPM, usando valor predeterminado');
        totalRate = 6.0; // Valor predeterminado (2.0 * 3 redes)
        networksFound = 3;
      }

      // Calcular el promedio de las tarifas
      final averageRate = totalRate / networksFound;
      print('Tarifa eCPM promedio para ${userProfile.country}: $averageRate');

      // Calcular ingresos por impresión (eCPM / 1000)
      final revenuePerAd = averageRate / 1000;

      // Calcular ingresos totales para los anuncios pendientes
      final totalNewRevenue = revenuePerAd * pendingAds;

      // Obtener la configuración global para el porcentaje de retiro
      final globalConfigDoc = await _firestore
          .collection('projects/pinataparty/config')
          .doc('global')
          .get();

      // Porcentaje predeterminado si no se encuentra configuración
      double withdrawalPercentage = 0.5; // 50% por defecto

      if (globalConfigDoc.exists) {
        withdrawalPercentage =
            (globalConfigDoc.data()?['withdrawalPercentage'] as num?)
                    ?.toDouble() ??
                0.5;
      }

      // Calcular ganancias disponibles para retiro
      final availableNewRevenue = totalNewRevenue * withdrawalPercentage;

      // Actualizar el documento del usuario con las ganancias estimadas
      // No actualizamos adEarnings ni availableAdEarnings, ya que eso lo hará el administrador
      // mediante el sistema de distribución de ganancias
      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userProfile.id)
          .update({
        'estimatedEarnings':
            availableNewRevenue, // Establecer las ganancias estimadas
        'processedRewardAds': userProfile
            .completedRewardAds, // Marcar anuncios como procesados para ganancias estimadas
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });

      print(
          'Ganancias estimadas actualizadas: $availableNewRevenue (de un total de $totalNewRevenue)');
    } catch (e) {
      print('Error al calcular ganancias: $e');
      throw e;
    }
  }
}

/// Estadísticas diarias de anuncios para un usuario
class DailyAdStats {
  final DateTime date;
  final int completedRewardAds;
  final Map<String, int> impressionsByNetwork;
  final Map<String, double> revenueByNetwork;
  final int totalImpressions;
  final double totalRevenue;

  DailyAdStats({
    required this.date,
    required this.completedRewardAds,
    required this.impressionsByNetwork,
    required this.revenueByNetwork,
    required this.totalImpressions,
    required this.totalRevenue,
  });

  /// Crea estadísticas diarias a partir de un documento de Firestore
  factory DailyAdStats.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DailyAdStats(
      date: (data['timestamp'] as Timestamp).toDate(),
      completedRewardAds: (data['completedRewardAds'] as num?)?.toInt() ?? 0,
      impressionsByNetwork: data['impressionsByNetwork'] != null
          ? Map<String, int>.from(data['impressionsByNetwork'] as Map)
          : {},
      revenueByNetwork: data['revenueByNetwork'] != null
          ? Map<String, double>.from(data['revenueByNetwork'] as Map)
          : {},
      totalImpressions: (data['totalImpressions'] as num?)?.toInt() ?? 0,
      totalRevenue: (data['totalRevenue'] as num?)?.toDouble() ?? 0.0,
    );
  }

  /// Convierte las estadísticas a un mapa para Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'timestamp': Timestamp.fromDate(date),
      'completedRewardAds': completedRewardAds,
      'impressionsByNetwork': impressionsByNetwork,
      'revenueByNetwork': revenueByNetwork,
      'totalImpressions': totalImpressions,
      'totalRevenue': totalRevenue,
    };
  }

  /// Ruta del documento para las estadísticas diarias
  static String documentPath(String userId, DateTime date) {
    final year = date.year.toString();
    final month = date.month.toString().padLeft(2, '0');
    final day = date.day.toString().padLeft(2, '0');
    return 'projects/pinataparty/users/$userId/adHistory/$year/$month/$day';
  }
}

/// Clase para gestionar los datos de usuario en el sistema web
class UserDataService {
  final FirebaseFirestore _firestore;

  UserDataService(this._firestore);

  /// Obtiene el perfil de un usuario
  Future<UserProfile?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .get();
      if (!doc.exists) {
        return null;
      }
      return UserProfile.fromFirestore(doc);
    } catch (e) {
      print('Error al obtener perfil de usuario: $e');
      return null;
    }
  }

  /// Actualiza el número de teléfono de un usuario
  Future<bool> updatePhoneNumber(String userId, String phoneNumber) async {
    try {
      await _firestore
          .collection('projects/pinataparty/users')
          .doc(userId)
          .update({
        'phoneNumber': phoneNumber,
        'lastUpdateAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('Error al actualizar número de teléfono: $e');
      return false;
    }
  }

  /// Obtiene las estadísticas diarias de un usuario
  Future<DailyAdStats?> getDailyStats(String userId, DateTime date) async {
    try {
      final docPath = DailyAdStats.documentPath(userId, date);
      final doc = await _firestore.doc(docPath).get();
      if (!doc.exists) {
        return null;
      }
      return DailyAdStats.fromFirestore(doc);
    } catch (e) {
      print('Error al obtener estadísticas diarias: $e');
      return null;
    }
  }

  /// Obtiene las impresiones de anuncios de un usuario en un rango de fechas
  Future<List<AdImpression>> getUserImpressions(
      String userId, DateTime startDate, DateTime endDate,
      {String? adNetwork}) async {
    try {
      Query query = _firestore
          .collection(AdImpression.collectionPath())
          .where('userId', isEqualTo: userId)
          .where('timestamp',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate));

      if (adNetwork != null) {
        query = query.where('adNetwork', isEqualTo: adNetwork);
      }

      final querySnapshot =
          await query.orderBy('timestamp', descending: true).get();

      return querySnapshot.docs
          .map((doc) => AdImpression.fromFirestore(
              doc as DocumentSnapshot<Map<String, dynamic>>, null))
          .toList();
    } catch (e) {
      print('Error al obtener impresiones de usuario: $e');
      return [];
    }
  }
}
