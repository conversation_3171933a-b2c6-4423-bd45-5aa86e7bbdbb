class GlobalConfig {
  final double withdrawalPercentage; // Porcentaje que pueden retirar (0.5 = 50%)
  final double minimumWithdrawal; // <PERSON><PERSON><PERSON> para poder retirar en USD
  final Map<String, double> networkBaseEcpm; // eCPM base por red
  final Map<String, Map<String, double>> countryMultipliers; // Multiplicadores por país

  GlobalConfig({
    this.withdrawalPercentage = 0.5,
    this.minimumWithdrawal = 10.0,
    this.networkBaseEcpm = const {
      'admob': 10.0,
      'unity': 8.0,
      'meta': 9.0,
    },
    this.countryMultipliers = const {
      'US': {'admob': 1.2, 'unity': 1.1, 'meta': 1.15},
      'MX': {'admob': 0.8, 'unity': 0.75, 'meta': 0.7},
      'default': {'admob': 0.5, 'unity': 0.45, 'meta': 0.4},
    },
  });

  GlobalConfig copyWith({
    double? withdrawalPercentage,
    double? minimumWithdrawal,
    Map<String, double>? networkBaseEcpm,
    Map<String, Map<String, double>>? countryMultipliers,
  }) {
    return GlobalConfig(
      withdrawalPercentage: withdrawalPercentage ?? this.withdrawalPercentage,
      minimumWithdrawal: minimumWithdrawal ?? this.minimumWithdrawal,
      networkBaseEcpm: networkBaseEcpm ?? this.networkBaseEcpm,
      countryMultipliers: countryMultipliers ?? this.countryMultipliers,
    );
  }
} 