import 'package:cloud_firestore/cloud_firestore.dart';

/// Modelo para impresiones de anuncios en el sistema web
///
/// Este modelo se utiliza para representar y analizar las impresiones de anuncios
/// almacenadas en 'projects/pinataparty/adAnalytics/impressions/records/{impressionId}'.
class AdImpression {
  final String id;
  final String userId;
  final DateTime timestamp;
  final String adType; // Siempre 'reward' en la implementación actual
  final String adNetwork; // Red publicitaria (AdMob, Unity, Meta)
  final bool completed; // Si el anuncio fue completado
  final String country; // País del usuario
  final String appVersion; // Versión de la aplicación

  // Campos opcionales
  final String? rewardType; // Tipo de recompensa (opcional)
  final int? rewardAmount; // Cantidad de recompensa (opcional)
  final double? revenue; // Ingresos calculados (gestionado por sistema web)
  final double?
      valueUSD; // Valor monetario del anuncio en USD (proporcionado por AdMob)
  final String?
      valuePrecision; // Precisión del valor (UNKNOWN, ESTIMATED, PRECISE, etc.)

  AdImpression({
    required this.id,
    required this.userId,
    required this.timestamp,
    required this.adType,
    required this.adNetwork,
    required this.completed,
    required this.country,
    required this.appVersion,
    this.rewardType,
    this.rewardAmount,
    this.revenue,
    this.valueUSD,
    this.valuePrecision,
  });

  factory AdImpression.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data()!;
    return AdImpression(
      id: snapshot.id,
      userId: data['userId'] as String,
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      adType: data['adType'] as String,
      adNetwork: data['adNetwork'] as String,
      completed: data['completed'] as bool,
      country: data['country'] as String,
      appVersion: data['appVersion'] as String,
      rewardType: data['rewardType'] as String?,
      rewardAmount: data['rewardAmount'] as int?,
      revenue:
          data['revenue'] != null ? (data['revenue'] as num).toDouble() : null,
      valueUSD: data['valueUSD'] != null
          ? (data['valueUSD'] as num).toDouble()
          : null,
      valuePrecision: data['valuePrecision'] as String?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'timestamp': Timestamp.fromDate(timestamp),
      'adType': adType,
      'adNetwork': adNetwork,
      'completed': completed,
      'country': country,
      'appVersion': appVersion,
      if (rewardType != null) 'rewardType': rewardType,
      if (rewardAmount != null) 'rewardAmount': rewardAmount,
      if (revenue != null) 'revenue': revenue,
      if (valueUSD != null) 'valueUSD': valueUSD,
      if (valuePrecision != null) 'valuePrecision': valuePrecision,
    };
  }

  // Métodos para trabajar con JSON (caché y serialización)
  factory AdImpression.fromJson(Map<String, dynamic> json) {
    return AdImpression(
      id: json['id'] as String,
      userId: json['userId'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      adType: json['adType'] as String,
      adNetwork: json['adNetwork'] as String,
      completed: json['completed'] as bool,
      country: json['country'] as String,
      appVersion: json['appVersion'] as String,
      rewardType: json['rewardType'] as String?,
      rewardAmount: json['rewardAmount'] as int?,
      revenue:
          json['revenue'] != null ? (json['revenue'] as num).toDouble() : null,
      valueUSD: json['valueUSD'] != null
          ? (json['valueUSD'] as num).toDouble()
          : null,
      valuePrecision: json['valuePrecision'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'timestamp': timestamp.toIso8601String(),
      'adType': adType,
      'adNetwork': adNetwork,
      'completed': completed,
      'country': country,
      'appVersion': appVersion,
      if (rewardType != null) 'rewardType': rewardType,
      if (rewardAmount != null) 'rewardAmount': rewardAmount,
      if (revenue != null) 'revenue': revenue,
      if (valueUSD != null) 'valueUSD': valueUSD,
      if (valuePrecision != null) 'valuePrecision': valuePrecision,
    };
  }

  /// Ruta de la colección donde se almacenan las impresiones
  static String collectionPath() {
    return 'projects/pinataparty/adAnalytics/impressions/records';
  }
}

/// Clase para registrar impresiones de anuncios desde el juego
///
/// Versión simplificada que solo incluye los campos necesarios para
/// enviar desde el juego al sistema web.
class GameAdImpression {
  final String userId;
  final DateTime timestamp;
  final String adNetwork;
  final String country;
  final String appVersion;
  final double?
      valueUSD; // Valor monetario del anuncio en USD (proporcionado por AdMob)
  final String?
      valuePrecision; // Precisión del valor (UNKNOWN, ESTIMATED, PRECISE, etc.)

  GameAdImpression({
    required this.userId,
    required this.timestamp,
    required this.adNetwork,
    required this.country,
    required this.appVersion,
    this.valueUSD,
    this.valuePrecision,
  });

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'timestamp': Timestamp.fromDate(timestamp),
      'adNetwork': adNetwork,
      'adType': 'reward', // Siempre es de tipo reward
      'completed': true, // Siempre se registran las completadas
      'country': country,
      'appVersion': appVersion,
      if (valueUSD != null) 'valueUSD': valueUSD,
      if (valuePrecision != null) 'valuePrecision': valuePrecision,
    };
  }

  /// Registra una impresión de anuncio en Firestore
  static Future<void> recordImpression(FirebaseFirestore firestore,
      String userId, String adNetwork, String country, String appVersion,
      {double? valueUSD, String? valuePrecision}) async {
    final Map<String, dynamic> impressionData = {
      'userId': userId,
      'timestamp': FieldValue.serverTimestamp(),
      'adNetwork': adNetwork,
      'adType': 'reward',
      'completed': true,
      'country': country,
      'appVersion': appVersion,
    };

    // Añadir campos opcionales si están presentes
    if (valueUSD != null) {
      impressionData['valueUSD'] = valueUSD;
    }
    if (valuePrecision != null) {
      impressionData['valuePrecision'] = valuePrecision;
    }

    await firestore
        .collection(AdImpression.collectionPath())
        .add(impressionData);

    // También actualizar el contador diario y total
    final now = DateTime.now();
    final year = now.year.toString();
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');

    // Actualizar contador diario
    final dailyRef = firestore
        .doc('projects/pinataparty/users/$userId/adHistory/$year/$month/$day');

    await dailyRef.set({
      'completedRewardAds': FieldValue.increment(1),
      'timestamp': FieldValue.serverTimestamp(),
    }, SetOptions(merge: true));

    // Actualizar contador total en users
    await firestore
        .collection('projects/pinataparty/users')
        .doc(userId)
        .update({
      'completedRewardAds': FieldValue.increment(1),
      'lastUpdateAt': FieldValue.serverTimestamp(),
    });
  }
}
