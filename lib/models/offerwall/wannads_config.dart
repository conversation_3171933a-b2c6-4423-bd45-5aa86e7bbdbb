class WannadsConfig {
  final String apiKey;
  final String offerwallBaseUrl;
  final String surveywallBaseUrl;

  const WannadsConfig({
    required this.apiKey,
    this.offerwallBaseUrl = 'https://earn.wannads.com/wall',
    this.surveywallBaseUrl = 'https://earn.wannads.com/surveywall',
  });

  String generateOfferwallUrl(String userId, {String? gender, String? age}) {
    String url = '$offerwallBaseUrl?apiKey=$apiKey&userId=$userId';
    if (gender != null && gender.isNotEmpty) {
      url += '&gender=$gender';
    }
    if (age != null && age.isNotEmpty) {
      url += '&age=$age';
    }
    return url;
  }

  String generateSurveywallUrl(String userId, {String? gender, String? age}) {
    String url = '$surveywallBaseUrl?apiKey=$apiKey&userId=$userId';
    if (gender != null && gender.isNotEmpty) {
      url += '&gender=$gender';
    }
    if (age != null && age.isNotEmpty) {
      url += '&age=$age';
    }
    return url;
  }

  // Default configuration for easy access
  static const WannadsConfig defaultConfig = WannadsConfig(
    apiKey: '68377ea67da48926033093', // Your PPReward API Key
  );
}
