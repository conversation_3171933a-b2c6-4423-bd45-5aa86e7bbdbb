/// Configuración para la integración con AdGem Offerwall
/// 
/// Esta clase maneja la configuración y generación de URLs para el offerwall de AdGem.
/// Basado en la documentación oficial de AdGem para integración web.
class AdGemConfig {
  static const String baseUrl = 'https://api.adgem.com/v1/wall';
  
  /// App ID proporcionado por AdGem para tu propiedad
  final String appId;
  
  /// URL de callback para recibir notificaciones de completación
  final String? callbackUrl;
  
  const AdGemConfig({
    required this.appId,
    this.callbackUrl,
  });
  
  /// Genera la URL completa del offerwall con el player ID y parámetros opcionales
  /// 
  /// [userId] debe ser lowercase alphanumeric según los requisitos de AdGem
  /// Los parámetros opcionales permiten mejor targeting y analytics
  String generateOfferwallUrl(String userId, {
    int? limit,
    String? appVersion,
    String? device,
    String? deviceName,
    String? ip,
    String? userAgent,
    String? osVersion,
    String? platform = 'web',
    int? playerAge,
    String? playerGender,
    bool? playerPayer,
    int? playerIapTotalUsd,
    DateTime? playerCreatedAt,
    int? playerLevel,
    int? placement,
    String? c1,
    String? c2,
    String? c3,
    String? c4,
    String? c5,
  }) {
    // Validar que el userId cumple con los requisitos de AdGem
    final cleanUserId = _validateAndCleanUserId(userId);
    
    final params = <String, String>{
      'appid': appId,
      'playerid': cleanUserId,
      if (limit != null) 'limit': limit.toString(),
      if (appVersion != null) 'app_version': appVersion,
      if (device != null) 'device': device,
      if (deviceName != null) 'device_name': deviceName,
      if (ip != null) 'ip': ip,
      if (userAgent != null) 'useragent': userAgent,
      if (osVersion != null) 'os_version': osVersion,
      if (platform != null) 'platform': platform,
      if (playerAge != null) 'player_age': playerAge.toString(),
      if (playerGender != null) 'player_gender': playerGender,
      if (playerPayer != null) 'player_payer': playerPayer.toString(),
      if (playerIapTotalUsd != null) 'player_iap_total_usd': playerIapTotalUsd.toString(),
      if (playerCreatedAt != null) 'player_created_at': _formatDateTime(playerCreatedAt),
      if (playerLevel != null) 'player_level': playerLevel.toString(),
      if (placement != null) 'placement': placement.toString(),
      if (c1 != null) 'c1': c1,
      if (c2 != null) 'c2': c2,
      if (c3 != null) 'c3': c3,
      if (c4 != null) 'c4': c4,
      if (c5 != null) 'c5': c5,
    };
    
    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return '$baseUrl?$queryString';
  }
  
  /// Valida y limpia el user ID según los requisitos de AdGem
  /// Debe ser lowercase alphanumeric solamente
  String _validateAndCleanUserId(String userId) {
    // Convertir a lowercase
    String cleanId = userId.toLowerCase();
    
    // Remover caracteres no permitidos (mantener solo a-z, 0-9, y guiones)
    cleanId = cleanId.replaceAll(RegExp(r'[^a-z0-9\-]'), '');
    
    // Validar que no esté vacío después de la limpieza
    if (cleanId.isEmpty) {
      throw ArgumentError('User ID must contain at least one alphanumeric character');
    }
    
    return cleanId;
  }
  
  /// Formatea DateTime para AdGem (sin timezone)
  String _formatDateTime(DateTime dateTime) {
    return dateTime.toIso8601String().split('T').first + ' ' + 
           dateTime.toIso8601String().split('T').last.split('.').first;
  }
  
  /// Configuración por defecto para Piñata Party
  static AdGemConfig get defaultConfig => const AdGemConfig(
    appId: '30519',
    callbackUrl: 'https://ppanaliticreward.web.app/api/adgem/callback',
  );
}
