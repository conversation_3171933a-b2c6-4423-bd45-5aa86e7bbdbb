class AyetConfig {
  final String adSlotId;
  final String? custom1;
  final String? custom2;
  final String? custom3;
  final String? custom4;
  final String? custom5;

  AyetConfig({
    required this.adSlotId,
    this.custom1,
    this.custom2,
    this.custom3,
    this.custom4,
    this.custom5,
  });

  String getOfferwallUrl(String userId) {
    final params = <String>[];
    
    if (custom1 != null) params.add('custom_1=$custom1');
    if (custom2 != null) params.add('custom_2=$custom2');
    if (custom3 != null) params.add('custom_3=$custom3');
    if (custom4 != null) params.add('custom_4=$custom4');
    if (custom5 != null) params.add('custom_5=$custom5');
    
    final queryParams = params.isNotEmpty ? '&${params.join('&')}' : '';
    
    return 'https://www.ayetstudios.com/offers/web_offerwall/$adSlotId?external_identifier=$userId$queryParams';
  }
}
