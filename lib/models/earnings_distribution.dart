import 'package:cloud_firestore/cloud_firestore.dart';

/// Modelo para representar una distribución de ganancias
///
/// Este modelo se utiliza para registrar las distribuciones de ganancias
/// realizadas por el administrador, incluyendo las fechas, tasas de eCPM,
/// y estadísticas de la distribución.
class EarningsDistribution {
  final String id;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime distributionDate;
  final String adminId;
  final String adminEmail;
  final Map<String, Map<String, double>> ecpmRates; // Tasas de eCPM por red y país
  final int totalProcessedAds;
  final double totalDistributedEarnings;
  final List<String> processedUserIds;
  final String status; // 'pending', 'processing', 'completed', 'failed'
  final String? errorMessage;
  final DateTime? completedAt;

  EarningsDistribution({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.distributionDate,
    required this.adminId,
    required this.adminEmail,
    required this.ecpmRates,
    required this.totalProcessedAds,
    required this.totalDistributedEarnings,
    required this.processedUserIds,
    required this.status,
    this.errorMessage,
    this.completedAt,
  });

  /// Crea un EarningsDistribution a partir de un documento de Firestore
  factory EarningsDistribution.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    // Convertir el mapa de ecpmRates
    final rawEcpmRates = data['ecpmRates'] as Map<String, dynamic>? ?? {};
    final Map<String, Map<String, double>> ecpmRates = {};
    
    for (var network in rawEcpmRates.keys) {
      final networkRates = rawEcpmRates[network] as Map<String, dynamic>? ?? {};
      ecpmRates[network] = {};
      
      for (var country in networkRates.keys) {
        ecpmRates[network]![country] = (networkRates[country] as num).toDouble();
      }
    }
    
    // Convertir la lista de userIds
    final rawUserIds = data['processedUserIds'] as List<dynamic>? ?? [];
    final List<String> processedUserIds = rawUserIds.map((id) => id.toString()).toList();
    
    return EarningsDistribution(
      id: doc.id,
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      distributionDate: (data['distributionDate'] as Timestamp).toDate(),
      adminId: data['adminId'] as String,
      adminEmail: data['adminEmail'] as String,
      ecpmRates: ecpmRates,
      totalProcessedAds: (data['totalProcessedAds'] as num).toInt(),
      totalDistributedEarnings: (data['totalDistributedEarnings'] as num).toDouble(),
      processedUserIds: processedUserIds,
      status: data['status'] as String,
      errorMessage: data['errorMessage'] as String?,
      completedAt: data['completedAt'] != null 
          ? (data['completedAt'] as Timestamp).toDate() 
          : null,
    );
  }

  /// Convierte la distribución a un mapa para Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'distributionDate': Timestamp.fromDate(distributionDate),
      'adminId': adminId,
      'adminEmail': adminEmail,
      'ecpmRates': ecpmRates,
      'totalProcessedAds': totalProcessedAds,
      'totalDistributedEarnings': totalDistributedEarnings,
      'processedUserIds': processedUserIds,
      'status': status,
      if (errorMessage != null) 'errorMessage': errorMessage,
      if (completedAt != null) 'completedAt': Timestamp.fromDate(completedAt!),
    };
  }

  /// Crea una copia de la distribución con algunos campos actualizados
  EarningsDistribution copyWith({
    String? id,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? distributionDate,
    String? adminId,
    String? adminEmail,
    Map<String, Map<String, double>>? ecpmRates,
    int? totalProcessedAds,
    double? totalDistributedEarnings,
    List<String>? processedUserIds,
    String? status,
    String? errorMessage,
    DateTime? completedAt,
  }) {
    return EarningsDistribution(
      id: id ?? this.id,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      distributionDate: distributionDate ?? this.distributionDate,
      adminId: adminId ?? this.adminId,
      adminEmail: adminEmail ?? this.adminEmail,
      ecpmRates: ecpmRates ?? this.ecpmRates,
      totalProcessedAds: totalProcessedAds ?? this.totalProcessedAds,
      totalDistributedEarnings: totalDistributedEarnings ?? this.totalDistributedEarnings,
      processedUserIds: processedUserIds ?? this.processedUserIds,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      completedAt: completedAt ?? this.completedAt,
    );
  }
}
