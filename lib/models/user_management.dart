import 'package:cloud_firestore/cloud_firestore.dart';

/// Modelo principal para la colección users
///
/// Este modelo unifica los datos que vienen del juego y los que gestiona el sistema web.
/// Se almacena en la colección 'projects/pinataparty/users/{userId}'.
///
/// Nota: Para acceder a campos adicionales como 'estimatedEarnings' y 'lastDistributionDate',
/// utilice el modelo extendido 'UserProfile' definido en 'user_data.dart'.
class UserManagement {
  // IDENTIFICACIÓN BÁSICA
  final String id; // ID de Firebase Auth
  final String email; // Email del usuario (obligatorio, desde el juego)
  final String?
      phoneNumber; // Número de teléfono (opcional, gestionado por web)
  final bool isActive; // Estado del usuario (gestionado por web)
  final bool isEmailVerified; // Si el correo ha sido verificado
  final bool isPhoneVerified; // Si el teléfono ha sido verificado

  // FECHAS
  final DateTime? createAt; // Fecha de registro (desde el juego)
  final DateTime? lastUpdateAt; // Última actualización (gestionado por web)
  final DateTime? lastLoginAt; // Último inicio de sesión (desde el juego)

  // UBICACIÓN
  final String country; // País del usuario (desde el juego)

  // DATOS FINANCIEROS (gestionados por sistema web)
  final double adEarnings; // Ganancias totales acumuladas a lo largo del tiempo
  final double
      availableAdEarnings; // Ganancias disponibles para retirar (confirmadas por el administrador)

  // MÉTRICAS DE ANUNCIOS (actualizadas desde el juego)
  final int completedRewardAds; // Total de anuncios de recompensa completados
  final int
      processedRewardAds; // Anuncios ya procesados para cálculo de ganancias

  // SISTEMA DE REFERIDOS (opcional, para futuras funcionalidades)
  final String? referralCode; // Código de referido
  final int referralCount; // Número de usuarios referidos
  final String? referredBy; // Quién refirió a este usuario
  final double totalReward; // Recompensas totales por referidos

  UserManagement({
    required this.id,
    required this.email,
    this.phoneNumber,
    this.isActive = true,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.createAt,
    this.lastUpdateAt,
    this.lastLoginAt,
    required this.country,
    this.adEarnings = 0.0,
    this.availableAdEarnings = 0.0,
    this.completedRewardAds = 0,
    this.processedRewardAds = 0,
    this.referralCode,
    this.referralCount = 0,
    this.referredBy,
    this.totalReward = 0.0,
  });

  factory UserManagement.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    // Manejar el caso donde los datos pueden ser nulos
    final data = snapshot.data() ?? {};

    return UserManagement(
      id: snapshot.id,
      email: data['email'] as String? ?? '<EMAIL>',
      phoneNumber: data['phoneNumber'] as String?,
      isActive: data['isActive'] as bool? ?? true,
      isEmailVerified: data['isEmailVerified'] as bool? ?? false,
      isPhoneVerified: data['isPhoneVerified'] as bool? ?? false,
      // Intentar obtener la fecha de registro de 'createAt' o 'createdAt'
      createAt: data['createAt'] != null
          ? (data['createAt'] as Timestamp).toDate()
          : data['createdAt'] != null
              ? (data['createdAt'] as Timestamp).toDate()
              : null,
      lastUpdateAt: data['lastUpdateAt'] != null
          ? (data['lastUpdateAt'] as Timestamp).toDate()
          : null,
      lastLoginAt: data['lastLoginAt'] != null
          ? (data['lastLoginAt'] as Timestamp).toDate()
          : null,
      country: data['country'] as String? ?? 'unknown',
      adEarnings: (data['adEarnings'] as num?)?.toDouble() ?? 0.0,
      availableAdEarnings:
          (data['availableAdEarnings'] as num?)?.toDouble() ?? 0.0,
      completedRewardAds: (data['completedRewardAds'] as num?)?.toInt() ?? 0,
      processedRewardAds: (data['processedRewardAds'] as num?)?.toInt() ?? 0,
      referralCode: data['referralCode'] as String?,
      referralCount: (data['referralCount'] as num?)?.toInt() ?? 0,
      referredBy: data['referredBy'] as String?,
      totalReward: (data['totalReward'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      if (phoneNumber != null) 'phoneNumber': phoneNumber,
      'isActive': isActive,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      if (createAt != null) 'createAt': Timestamp.fromDate(createAt!),
      if (lastUpdateAt != null)
        'lastUpdateAt': Timestamp.fromDate(lastUpdateAt!),
      if (lastLoginAt != null) 'lastLoginAt': Timestamp.fromDate(lastLoginAt!),
      'country': country,
      'adEarnings': adEarnings,
      'availableAdEarnings': availableAdEarnings,
      'completedRewardAds': completedRewardAds,
      'processedRewardAds': processedRewardAds,
      if (referralCode != null) 'referralCode': referralCode,
      'referralCount': referralCount,
      if (referredBy != null) 'referredBy': referredBy,
      'totalReward': totalReward,
    };
  }

  UserManagement copyWith({
    String? id,
    String? email,
    String? phoneNumber,
    bool? isActive,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    DateTime? createAt,
    DateTime? lastUpdateAt,
    DateTime? lastLoginAt,
    String? country,
    double? adEarnings,
    double? availableAdEarnings,
    int? completedRewardAds,
    int? processedRewardAds,
    String? referralCode,
    int? referralCount,
    String? referredBy,
    double? totalReward,
  }) {
    return UserManagement(
      id: id ?? this.id,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      createAt: createAt ?? this.createAt,
      lastUpdateAt: lastUpdateAt ?? this.lastUpdateAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      country: country ?? this.country,
      adEarnings: adEarnings ?? this.adEarnings,
      availableAdEarnings: availableAdEarnings ?? this.availableAdEarnings,
      completedRewardAds: completedRewardAds ?? this.completedRewardAds,
      processedRewardAds: processedRewardAds ?? this.processedRewardAds,
      referralCode: referralCode ?? this.referralCode,
      referralCount: referralCount ?? this.referralCount,
      referredBy: referredBy ?? this.referredBy,
      totalReward: totalReward ?? this.totalReward,
    );
  }
}

/// Clase para datos que se envían desde el juego al registrarse o iniciar sesión
class GameUserData {
  final String id;
  final String email;
  final String country;
  final DateTime registeredAt;
  final DateTime lastLoginAt;

  GameUserData({
    required this.id,
    required this.email,
    required this.country,
    required this.registeredAt,
    required this.lastLoginAt,
  });

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'country': country,
      'createAt': Timestamp.fromDate(registeredAt),
      'lastLoginAt': Timestamp.fromDate(lastLoginAt),
    };
  }
}

/// Clase para registrar impresiones de anuncios desde el juego
///
/// Se almacena en 'projects/pinataparty/adAnalytics/impressions/records'
class AdImpressionRecord {
  final String userId;
  final DateTime timestamp;
  final String adNetwork;
  final bool completed;
  final String country;
  final String appVersion;
  final double?
      valueUSD; // Valor monetario del anuncio en USD (proporcionado por AdMob)
  final String?
      valuePrecision; // Precisión del valor (UNKNOWN, ESTIMATED, PRECISE, etc.)

  AdImpressionRecord({
    required this.userId,
    required this.timestamp,
    required this.adNetwork,
    required this.completed,
    required this.country,
    required this.appVersion,
    this.valueUSD,
    this.valuePrecision,
  });

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'timestamp': Timestamp.fromDate(timestamp),
      'adNetwork': adNetwork,
      'adType': 'reward', // Siempre es de tipo reward
      'completed': completed,
      'country': country,
      'appVersion': appVersion,
      if (valueUSD != null) 'valueUSD': valueUSD,
      if (valuePrecision != null) 'valuePrecision': valuePrecision,
    };
  }

  // Ruta donde se almacena en Firestore
  static String collectionPath() {
    return 'projects/pinataparty/adAnalytics/impressions/records';
  }
}

/// Clase para actualizar contadores diarios desde el juego
///
/// Se almacena en 'projects/pinataparty/users/{userId}/adHistory/{year}/{month}/{day}'
class DailyAdCounter {
  final String userId;
  final DateTime date;
  final int completedRewardAds;

  DailyAdCounter({
    required this.userId,
    required this.date,
    required this.completedRewardAds,
  });

  Map<String, dynamic> toFirestore() {
    return {
      'completedRewardAds': completedRewardAds,
      'timestamp': Timestamp.fromDate(DateTime.now()),
    };
  }

  // Ruta donde se almacena en Firestore
  String documentPath() {
    final year = date.year.toString();
    final month = date.month.toString().padLeft(2, '0');
    final day = date.day.toString().padLeft(2, '0');
    return 'projects/pinataparty/users/$userId/adHistory/$year/$month/$day';
  }
}

/// Clase auxiliar para crear/actualizar usuarios desde el juego
class UserRegistration {
  /// Registra o actualiza un usuario en la colección users
  static Future<void> registerOrUpdateUser(
    FirebaseFirestore firestore,
    String userId,
    String email,
    String country,
  ) async {
    // Actualizar/crear en la colección users
    await firestore.collection('projects/pinataparty/users').doc(userId).set({
      'email': email,
      'country': country,
      'createAt': FieldValue.serverTimestamp(),
      'lastLoginAt': FieldValue.serverTimestamp(),
      'isActive': true,
      'isEmailVerified': false,
      'isPhoneVerified': false,
      'completedRewardAds': 0, // Inicializar si es nuevo
      'processedRewardAds': 0, // Inicializar contador de anuncios procesados
      'adEarnings': 0.0,
      'availableAdEarnings': 0.0,
    }, SetOptions(merge: true));
  }

  /// Registra una impresión de anuncio y actualiza contadores
  static Future<void> recordAdImpression(
      FirebaseFirestore firestore,
      String userId,
      String email,
      String country,
      String adNetwork,
      String appVersion,
      {double? valueUSD,
      String? valuePrecision}) async {
    // 1. Registrar impresión
    final Map<String, dynamic> impressionData = {
      'userId': userId,
      'timestamp': FieldValue.serverTimestamp(),
      'adNetwork': adNetwork,
      'adType': 'reward',
      'completed': true,
      'country': country,
      'appVersion': appVersion,
    };

    // Añadir campos opcionales si están presentes
    if (valueUSD != null) {
      impressionData['valueUSD'] = valueUSD;
    }
    if (valuePrecision != null) {
      impressionData['valuePrecision'] = valuePrecision;
    }

    await firestore
        .collection(AdImpressionRecord.collectionPath())
        .add(impressionData);

    // 2. Actualizar contador diario
    final now = DateTime.now();
    final year = now.year.toString();
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');

    final dailyRef = firestore
        .doc('projects/pinataparty/users/$userId/adHistory/$year/$month/$day');

    await dailyRef.set({
      'completedRewardAds': FieldValue.increment(1),
      'timestamp': FieldValue.serverTimestamp(),
    }, SetOptions(merge: true));

    // 3. Actualizar contador total en users
    await firestore
        .collection('projects/pinataparty/users')
        .doc(userId)
        .update({
      'completedRewardAds': FieldValue.increment(1),
      'lastUpdateAt': FieldValue.serverTimestamp(),
    });
  }
}
