import 'package:cloud_firestore/cloud_firestore.dart';

class ECPMConfig {
  final String adNetwork;
  final String adType;
  final Map<String, double> countryRates;
  final double defaultRate;
  final DateTime lastUpdate;

  ECPMConfig({
    required this.adNetwork,
    required this.adType,
    required this.countryRates,
    required this.defaultRate,
    required this.lastUpdate,
  });

  double getRateForCountry(String country) {
    return countryRates[country] ?? defaultRate;
  }

  factory ECPMConfig.fromFirestore(
    DocumentSnapshot snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data() as Map<String, dynamic>;
    return ECPMConfig(
      adNetwork: data['adNetwork'] as String,
      adType: data['adType'] as String,
      countryRates: Map<String, double>.from(data['countryRates'] as Map),
      defaultRate: (data['defaultRate'] as num).toDouble(),
      lastUpdate: (data['lastUpdate'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'adNetwork': adNetwork,
      'adType': adType,
      'countryRates': countryRates,
      'defaultRate': defaultRate,
      'lastUpdate': Timestamp.fromDate(lastUpdate),
    };
  }

  factory ECPMConfig.defaultConfig(String adNetwork, String adType) {
    return ECPMConfig(
      adNetwork: adNetwork,
      adType: adType,
      countryRates: {
        'US': 10.0,
        'GB': 8.0,
        'DE': 7.0,
        'FR': 6.0,
        'JP': 9.0,
        'KR': 7.0,
        'AU': 7.5,
        'CA': 8.0,
      },
      defaultRate: 2.0,
      lastUpdate: DateTime.now(),
    );
  }

  ECPMConfig copyWith({
    String? adNetwork,
    String? adType,
    Map<String, double>? countryRates,
    double? defaultRate,
    DateTime? lastUpdate,
  }) {
    return ECPMConfig(
      adNetwork: adNetwork ?? this.adNetwork,
      adType: adType ?? this.adType,
      countryRates: countryRates ?? Map<String, double>.from(this.countryRates),
      defaultRate: defaultRate ?? this.defaultRate,
      lastUpdate: lastUpdate ?? this.lastUpdate,
    );
  }
}