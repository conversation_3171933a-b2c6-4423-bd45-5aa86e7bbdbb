import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Widget para mostrar errores relacionados con índices de Firestore
class IndexErrorDisplay extends StatelessWidget {
  final String message;
  final String indexDefinition;
  final VoidCallback? onRetry;

  const IndexErrorDisplay({
    super.key,
    required this.message,
    required this.indexDefinition,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error de Índice en Firestore',
                  style: Theme.of(context).textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  message,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                const Text(
                  'Para solucionar este problema:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  '1. Ve a la consola de Firebase > Firestore > Índices',
                  textAlign: TextAlign.center,
                ),
                const Text(
                  '2. Crea un nuevo índice compuesto con la siguiente configuración:',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          indexDefinition,
                          style: const TextStyle(fontFamily: 'monospace'),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.copy),
                        tooltip: 'Copiar definición',
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: indexDefinition));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Definición copiada al portapapeles'),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  '3. O usa el archivo firestore.indexes.json incluido en el proyecto',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                if (onRetry != null)
                  ElevatedButton(
                    onPressed: onRetry,
                    child: const Text('Reintentar'),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
