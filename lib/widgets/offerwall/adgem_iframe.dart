import 'dart:html' as html;
import 'dart:ui_web' as ui_web;
import 'package:flutter/material.dart';
import '../../models/offerwall/adgem_config.dart';

/// Widget que muestra el offerwall de AdGem en un iframe
///
/// Este widget integra el offerwall de AdGem usando iframe para Flutter Web.
/// Maneja la creación del iframe, parámetros de usuario y eventos de carga.
class AdGemIframe extends StatefulWidget {
  /// ID único del usuario (será convertido a lowercase alphanumeric)
  final String userId;

  /// Configuración de AdGem
  final AdGemConfig config;

  /// Altura del iframe (por defecto 600px)
  final double height;

  /// Ancho del iframe (por defecto 100%)
  final double? width;

  /// Callback cuando el iframe se carga exitosamente
  final VoidCallback? onLoad;

  /// Callback cuando hay un error cargando el iframe
  final Function(String error)? onError;

  /// Parámetros adicionales para enviar a AdGem
  final Map<String, dynamic>? additionalParams;

  const AdGemIframe({
    Key? key,
    required this.userId,
    required this.config,
    this.height = 600,
    this.width,
    this.onLoad,
    this.onError,
    this.additionalParams,
  }) : super(key: key);

  @override
  State<AdGemIframe> createState() => _AdGemIframeState();
}

class _AdGemIframeState extends State<AdGemIframe> {
  late String viewId;
  // bool _isLoading = true; // No longer used for primary rendering logic
  String? _error;

  @override
  void initState() {
    super.initState();
    viewId =
        'adgem-iframe-${widget.userId}-${DateTime.now().millisecondsSinceEpoch}';
    _registerIframe();
  }

  void _registerIframe() {
    try {
      // Generar URL del offerwall con parámetros
      final url = widget.config.generateOfferwallUrl(
        widget.userId,
        appVersion: '1.0.0',
        platform: 'web',
        c1: 'pinata_party',
        c2: widget.additionalParams?['country'],
        playerLevel: widget.additionalParams?['level'],
        playerAge: widget.additionalParams?['age'],
      );

      print('AdGem Offerwall URL: $url');

      // Crear el iframe
      final iframe = html.IFrameElement()
        ..src = url
        ..style.border = 'none'
        ..style.width = widget.width != null ? '${widget.width}px' : '100%'
        ..style.height = '${widget.height}px'
        ..style.borderRadius = '8px'
        ..allowFullscreen = true
        ..setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-presentation allow-top-navigation-by-user-activation')
        ..setAttribute('loading', 'lazy');

      // Agregar event listeners
      iframe.onLoad.listen((_) {
        if (mounted) {
          // setState(() { _isLoading = false; }); // No longer strictly needed for rendering
          _error = null; // Clear any previous error on successful load
          widget.onLoad?.call();
        }
      });

      iframe.onError.listen((event) {
        if (mounted) {
          setState(() {
            _error = 'Error cargando el offerwall de AdGem. Event: $event';
          });
          print('AdGemIframe onError: $_error for URL: ${iframe.src}');
          widget.onError?.call('Error loading AdGem iframe');
        }
      });

      // Registrar el elemento para Flutter Web
      ui_web.platformViewRegistry.registerViewFactory(
        viewId,
        (int viewId) => iframe,
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Error configurando el offerwall de AdGem: $e';
        });
        print('AdGemIframe catch error: $_error');
      }
      widget.onError?.call(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      print('Error in AdGemIframe ($viewId): $_error');
    }

    return Container(
      height: widget.height,
      width: widget.width,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(7),
        child: HtmlElementView(viewType: viewId),
      ),
    );
  }
}
