// ignore_for_file: avoid_web_libraries_in_flutter

import 'dart:html' as html;
import 'dart:ui_web' as ui_web;
import 'package:flutter/material.dart';
import '../../models/offerwall/wannads_config.dart';

class WannadsIframe extends StatefulWidget {
  final String userId;
  final WannadsConfig config;
  final double height;
  final double? width;
  final VoidCallback? onLoad;
  final Function(String error)? onError;
  final String? gender;
  final String? age;
  final bool isSurveyWall;

  const WannadsIframe({
    Key? key,
    required this.userId,
    required this.config,
    this.height = 800, // Default height from Wannads example
    this.width,
    this.onLoad,
    this.onError,
    this.gender,
    this.age,
    this.isSurveyWall = false, // Default to Offerwall
  }) : super(key: key);

  @override
  State<WannadsIframe> createState() => _WannadsIframeState();
}

class _WannadsIframeState extends State<WannadsIframe> {
  late String viewId;
  // bool _isLoading = true; // No longer used for primary rendering logic
  String? _error;

  @override
  void initState() {
    super.initState();
    viewId =
        'wannads-iframe-${widget.userId}-${DateTime.now().millisecondsSinceEpoch}';
    _registerIframe();
  }

  void _registerIframe() {
    try {
      final String url = widget.isSurveyWall
          ? widget.config.generateSurveywallUrl(
              widget.userId,
              gender: widget.gender,
              age: widget.age,
            )
          : widget.config.generateOfferwallUrl(
              widget.userId,
              gender: widget.gender,
              age: widget.age,
            );

      // ignore: avoid_print
      print('Wannads Iframe URL: $url');

      final iframe = html.IFrameElement()
        ..src = url
        ..style.border = 'none'
        ..style.padding = '0'
        ..style.margin = '0'
        ..style.width = widget.width != null ? '${widget.width}px' : '100%'
        ..style.height = '${widget.height}px'
        ..allowFullscreen = true
        ..setAttribute('scrolling', 'yes')
        ..setAttribute('frameborder', '0')
        ..setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-presentation allow-top-navigation-by-user-activation');

      iframe.onLoad.listen((_) {
        if (mounted) {
          // setState(() { _isLoading = false; }); // No longer strictly needed for rendering
          _error = null; // Clear any previous error on successful load
          widget.onLoad?.call();
        }
      });

      iframe.onError.listen((event) {
        if (mounted) {
          setState(() {
            // _isLoading = false; // No longer strictly needed for rendering
            _error = 'Error cargando el offerwall de Wannads. Event: $event';
          });
          print('WannadsIframe onError: $_error for URL: ${iframe.src}');
          widget.onError?.call('Error loading Wannads iframe');
        }
      });

      ui_web.platformViewRegistry.registerViewFactory(
        viewId,
        (int viewId) => iframe,
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          // _isLoading = false; // No longer strictly needed for rendering
          _error = 'Error configurando el offerwall de Wannads: $e';
        });
        print('WannadsIframe catch error: $_error');
      }
      widget.onError?.call(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      print('Error in WannadsIframe ($viewId): $_error');
    }

    return Container(
      height: widget.height,
      width: widget.width,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(7),
        child: HtmlElementView(viewType: viewId),
      ),
    );
  }
}
