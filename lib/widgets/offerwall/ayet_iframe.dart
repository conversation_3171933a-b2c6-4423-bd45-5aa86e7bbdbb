// ignore_for_file: avoid_web_libraries_in_flutter

import 'dart:html' as html;
import 'dart:ui_web' as ui_web;
import 'package:flutter/material.dart';
import '../../models/offerwall/ayet_config.dart';

class AyetIframe extends StatefulWidget {
  final String userId;
  final AyetConfig config;
  final double height;
  final double? width;
  final VoidCallback? onLoad;
  final Function(String error)? onError;

  const AyetIframe({
    Key? key,
    required this.userId,
    required this.config,
    this.height = 800,
    this.width,
    this.onLoad,
    this.onError,
  }) : super(key: key);

  @override
  State<AyetIframe> createState() => _AyetIframeState();
}

class _AyetIframeState extends State<AyetIframe> {
  final String _viewType = 'ayet-iframe-${DateTime.now().millisecondsSinceEpoch}';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _registerIframe();
  }

  void _registerIframe() {
    // Register the iframe with the platform view registry
    ui_web.platformViewRegistry.registerViewFactory(
      _viewType,
      (int viewId) {
        final iframe = html.IFrameElement()
          ..style.border = 'none'
          ..style.width = '100%'
          ..style.height = '100%'
          ..src = widget.config.getOfferwallUrl(widget.userId);

        // Handle iframe load events
        iframe.onLoad.listen((event) {
          if (widget.onLoad != null) {
            widget.onLoad!();
          }
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        });

        // Handle iframe errors
        iframe.onError.listen((event) {
          if (widget.onError != null) {
            widget.onError!('Failed to load AyetStudios offerwall');
          }
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        });

        return iframe;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width ?? double.infinity,
      height: widget.height,
      child: Stack(
        children: [
          // The actual iframe
          HtmlElementView(
            viewType: _viewType,
          ),
          
          // Loading indicator
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
