import 'package:flutter/material.dart';
import '../../models/analytics_data.dart';

class Impressions<PERSON>hart extends StatelessWidget {
  final AnalyticsData data;
  
  const ImpressionsChart({
    required this.data,
    super.key,
  });

  // Constructor alternativo para compatibilidad con código existente
  ImpressionsChart.fromAnalyticsData({
    required AnalyticsData analyticsData,
    super.key,
  }) : data = analyticsData;

  @override
  Widget build(BuildContext context) {
    final networks = data.impressionsByAdType.entries.toList();
    
    if (networks.isEmpty) {
      return const Card(
        margin: EdgeInsets.symmetric(vertical: 8),
        child: Padding(
          padding: EdgeInsets.all(36.0),
          child: Center(
            child: Text('No impression data available'),
          ),
        ),
      );
    }
    
    // Ordenar por cantidad de impresiones (de mayor a menor)
    networks.sort((a, b) => b.value.compareTo(a.value));
    
    // Definimos colores para las diferentes redes
    final Map<String, Color> networkColors = {
      'AdMob': Colors.red.shade400,
      'Unity': Colors.blue.shade400,
      'Meta': Colors.green.shade400,
      // Color predeterminado para redes desconocidas
      'default': Colors.grey.shade400,
    };
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Impressions by Network',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: networks.length,
              itemBuilder: (context, index) {
                final network = networks[index];
                final percentage = network.value / data.totalImpressions * 100;
                final color = networkColors[network.key] ?? networkColors['default']!;
                
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                color: color,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                network.key,
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          Text(
                            '${network.value} (${percentage.toStringAsFixed(1)}%)',
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: percentage / 100,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(color),
                        minHeight: 8,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
} 