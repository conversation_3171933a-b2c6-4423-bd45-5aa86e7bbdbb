import 'package:flutter/material.dart';
import '../../models/analytics_data.dart';

class RevenueChart extends StatelessWidget {
  final AnalyticsData analyticsData;
  
  const RevenueChart({
    required this.analyticsData,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return RevenueBarChart(data: analyticsData);
  }
}

class RevenueBarChart extends StatelessWidget {
  final AnalyticsData data;
  
  const RevenueBarChart({
    required this.data,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final revenueData = data.revenueByCountry.entries.toList();
    
    // Ordenar por ingresos (de mayor a menor)
    revenueData.sort((a, b) => b.value.compareTo(a.value));
    
    // Tomar los 5 primeros o menos si hay menos
    final topCountries = revenueData.take(5).toList();
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Revenue by Country',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 24),
              if (topCountries.isEmpty)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: Text('No revenue data available'),
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: topCountries.length,
                  itemBuilder: (context, index) {
                    final item = topCountries[index];
                    final percentage = item.value / data.totalRevenue * 100;
                    
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                item.key,
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Text(
                                '\$${item.value.toStringAsFixed(2)} (${percentage.toStringAsFixed(1)}%)',
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: item.value / (topCountries.first.value * 1.2),
                            backgroundColor: Colors.grey.shade200,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              index == 0 ? Colors.blue :
                              index == 1 ? Colors.green :
                              index == 2 ? Colors.amber :
                              index == 3 ? Colors.purple :
                              Colors.teal,
                            ),
                            minHeight: 8,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ],
                      ),
                    );
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }
} 