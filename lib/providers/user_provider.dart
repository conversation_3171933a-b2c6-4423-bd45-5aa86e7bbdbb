import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_data.dart';
import '../services/user_service.dart';
import '../services/phone_auth_service.dart';
import '../services/email_auth_service.dart';
import 'package:firebase_auth/firebase_auth.dart';

// Proveedor para el perfil del usuario actual
final userProfileProvider = FutureProvider<UserProfile?>((ref) async {
  return await UserService.getCurrentUserProfile();
});

// Proveedor para actualizar el número de teléfono
final updatePhoneProvider = FutureProvider.autoDispose.family<bool, String>((ref, phoneNumber) async {
  final result = await UserService.updatePhoneNumber(phoneNumber);
  if (result) {
    // Invalidar el proveedor de perfil para refrescar los datos
    ref.invalidate(userProfileProvider);
  }
  return result;
});

// Proveedor para actualizar el correo electrónico
final updateEmailProvider = FutureProvider.autoDispose.family<bool, ({String email, String password})>((ref, params) async {
  final result = await UserService.updateEmail(params.email, params.password);
  if (result) {
    // Invalidar el proveedor de perfil para refrescar los datos
    ref.invalidate(userProfileProvider);
  }
  return result;
});

// Estado para controlar el proceso de verificación de correo electrónico
final emailVerificationStateProvider = StateProvider<EmailVerificationState>((ref) => EmailVerificationState.initial);

// Proveedor para enviar correo de verificación
final sendVerificationEmailProvider = FutureProvider.autoDispose<bool>((ref) async {
  ref.read(emailVerificationStateProvider.notifier).state = EmailVerificationState.sending;

  final result = await EmailAuthService.sendVerificationEmail();

  if (result) {
    ref.read(emailVerificationStateProvider.notifier).state = EmailVerificationState.sent;
  } else {
    ref.read(emailVerificationStateProvider.notifier).state = EmailVerificationState.failed;
  }

  return result;
});

// Proveedor para verificar si el correo electrónico ha sido verificado
final checkEmailVerifiedProvider = FutureProvider.autoDispose<bool>((ref) async {
  final result = await EmailAuthService.checkEmailVerified();

  if (result) {
    ref.read(emailVerificationStateProvider.notifier).state = EmailVerificationState.verified;
    ref.invalidate(userProfileProvider);
  }

  return result;
});

// Enum para el estado de verificación del correo electrónico
enum EmailVerificationState {
  initial,
  sending,
  sent,
  verified,
  failed
}

// Mantenemos el provider original para compatibilidad
final verifyEmailProvider = FutureProvider.autoDispose<bool>((ref) async {
  return await EmailAuthService.checkEmailVerified();
});

// Estado para almacenar el ID de verificación del teléfono
final phoneVerificationIdProvider = StateProvider<String?>((ref) => null);

// Estado para controlar el proceso de verificación de teléfono
final phoneVerificationStateProvider = StateProvider<PhoneVerificationState>((ref) => PhoneVerificationState.initial);

// Proveedor para iniciar la verificación del número de teléfono
final startPhoneVerificationProvider = FutureProvider.autoDispose.family<bool, (String, dynamic)>((ref, params) async {
  final phoneNumber = params.$1;
  final recaptchaVerifier = params.$2;

  // Primero cambiamos el estado a inicial para evitar problemas con Riverpod
  ref.read(phoneVerificationStateProvider.notifier).state = PhoneVerificationState.initial;

  // Luego, en un Future separado, cambiamos el estado a 'sending'
  Future(() {
    ref.read(phoneVerificationStateProvider.notifier).state = PhoneVerificationState.sending;
  });

  final result = await PhoneAuthService.verifyPhoneNumber(
    phoneNumber: phoneNumber,
    recaptchaVerifier: recaptchaVerifier,
    onCodeSent: (verificationId) {
      ref.read(phoneVerificationIdProvider.notifier).state = verificationId;
      ref.read(phoneVerificationStateProvider.notifier).state = PhoneVerificationState.codeSent;
    },
    onVerificationFailed: (exception) {
      ref.read(phoneVerificationStateProvider.notifier).state = PhoneVerificationState.failed;
    },
    onVerificationCompleted: () {
      ref.read(phoneVerificationStateProvider.notifier).state = PhoneVerificationState.verified;
      ref.invalidate(userProfileProvider);
    },
  );
  return result;
});

// Proveedor para verificar el código SMS
final verifySmsCodeProvider = FutureProvider.autoDispose.family<bool, String>((ref, smsCode) async {
  final result = await PhoneAuthService.verifySmsCode(smsCode);
  if (result) {
    ref.read(phoneVerificationStateProvider.notifier).state = PhoneVerificationState.verified;
    ref.invalidate(userProfileProvider);
  } else {
    ref.read(phoneVerificationStateProvider.notifier).state = PhoneVerificationState.failed;
  }
  return result;
});

// Enum para el estado de verificación del teléfono
enum PhoneVerificationState {
  initial,
  sending,
  codeSent,
  verified,
  failed
}

// Proveedor para solicitar un retiro
final requestWithdrawalProvider = FutureProvider.autoDispose.family<bool, ({double amount, String method, Map<String, dynamic> details})>((ref, params) async {
  final result = await UserService.requestWithdrawal(
    params.amount,
    params.method,
    params.details,
  );
  if (result) {
    // Invalidar el proveedor de perfil para refrescar los datos
    ref.invalidate(userProfileProvider);
  }
  return result;
});

// Proveedor para obtener las solicitudes de retiro del usuario
final userWithdrawalsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  return await UserService.getUserWithdrawals();
});
