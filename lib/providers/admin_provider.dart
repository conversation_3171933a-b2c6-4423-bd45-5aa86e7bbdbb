import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/withdrawal_management.dart';
import '../models/user_management.dart';
import '../models/user_data.dart';
import '../services/admin_service.dart';

// Providers para la gestión de retiros
final pendingWithdrawalsProvider =
    FutureProvider<List<WithdrawalManagement>>((ref) async {
  return await AdminService.getPendingWithdrawals();
});

// Clase para manejar el estado de los retiros procesados
class ProcessedWithdrawalsNotifier
    extends StateNotifier<AsyncValue<List<WithdrawalManagement>>> {
  ProcessedWithdrawalsNotifier() : super(const AsyncValue.loading());

  String? _status;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isLoading = false;

  Future<void> loadWithdrawals({
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // Verificar si los parámetros son iguales a los actuales para evitar consultas innecesarias
    if (_isLoading ||
        (_status == status &&
            _startDate == startDate &&
            _endDate == endDate &&
            state is! AsyncError)) {
      return;
    }

    _isLoading = true;
    _status = status;
    _startDate = startDate;
    _endDate = endDate;

    state = const AsyncValue.loading();

    try {
      final withdrawals = await AdminService.getProcessedWithdrawals(
        status: status,
        startDate: startDate,
        endDate: endDate,
      );

      if (mounted) {
        state = AsyncValue.data(withdrawals);
      }
    } catch (e, stack) {
      if (mounted) {
        state = AsyncValue.error(e, stack);
      }
    } finally {
      _isLoading = false;
    }
  }

  void refresh() {
    loadWithdrawals(
      status: _status,
      startDate: _startDate,
      endDate: _endDate,
    );
  }
}

final processedWithdrawalsProvider = StateNotifierProvider<
    ProcessedWithdrawalsNotifier,
    AsyncValue<List<WithdrawalManagement>>>((ref) {
  return ProcessedWithdrawalsNotifier();
});

final withdrawalSettingsProvider =
    FutureProvider<WithdrawalSettings>((ref) async {
  return await AdminService.getWithdrawalSettings();
});

// Providers para la gestión de usuarios
final usersProvider =
    FutureProvider.family<List<UserManagement>, Map<String, dynamic>>(
        (ref, params) async {
  return await AdminService.getUsers(
    country: params['country'] as String?,
    isActive: params['isActive'] as bool?,
    limit: params['limit'] as int? ?? 50,
    startAfter: params['startAfter'] as DocumentSnapshot?,
  );
});

final userDetailProvider =
    FutureProvider.family<UserProfile, String>((ref, userId) async {
  return await AdminService.getUserDetail(userId);
});

final userWithdrawalsProvider =
    FutureProvider.family<List<WithdrawalManagement>, String>(
        (ref, userId) async {
  return await AdminService.getUserWithdrawals(userId);
});

// Providers para los logs de administración
final adminLogsProvider =
    FutureProvider.family<List<AdminLog>, Map<String, dynamic>>(
        (ref, params) async {
  return await AdminService.getAdminLogs(
    action: params['action'] as String?,
    startDate: params['startDate'] as DateTime?,
    endDate: params['endDate'] as DateTime?,
    limit: params['limit'] as int? ?? 50,
  );
});

// Providers para estados de filtros
final selectedWithdrawalStatusProvider = StateProvider<String?>((ref) => null);
final selectedWithdrawalDateRangeProvider =
    StateProvider<DateTimeRange?>((ref) => null);

final selectedUserCountryProvider = StateProvider<String?>((ref) => null);
final selectedUserStatusProvider = StateProvider<bool?>((ref) => null);

// Clase auxiliar para el rango de fechas
class DateTimeRange {
  final DateTime start;
  final DateTime end;

  DateTimeRange({required this.start, required this.end});
}
