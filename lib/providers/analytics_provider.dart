import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/firebase_service.dart';
import '../services/cache_service.dart';
import '../models/analytics_data.dart';
import '../models/ecpm_config.dart';
import '../services/analytics_service.dart';
import '../models/user_data.dart';
import '../models/ad_impression.dart';
import '../models/user_analytics_data.dart';

// Clase para llevar el progreso de carga de datos
class LoadingProgress {
  final int total;
  final int current;

  LoadingProgress({required this.total, required this.current});

  double get progress => total > 0 ? current / total : 0.0;
}

final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return AnalyticsService();
});

// Loading progress state
final analyticsLoadingProgressProvider = StateProvider<LoadingProgress>((ref) {
  return LoadingProgress(total: 0, current: 0);
});

final dateRangeProvider =
    StateProvider<({DateTime startDate, DateTime endDate})>((ref) {
  final now = DateTime.now();
  // Por defecto, mostrar solo el día actual
  return (
    startDate: DateTime(now.year, now.month, now.day),
    endDate: now,
  );
});

final selectedNetworkProvider = StateProvider<String?>((ref) => null);
final selectedCountryProvider = StateProvider<String?>((ref) => null);

// Obtener datos directamente de Firebase
final analyticsDataProvider = FutureProvider<AnalyticsData>((ref) async {
  final dateRange = ref.watch(dateRangeProvider);
  final progressNotifier = ref.read(analyticsLoadingProgressProvider.notifier);

  // Configurar el callback de progreso
  final progressCallback = (int current, int total) {
    progressNotifier.state = LoadingProgress(current: current, total: total);
  };

  return await AnalyticsService.fetchGlobalAnalyticsData(
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
    onProgress: progressCallback,
  );
});

final ecpmConfigProvider =
    FutureProvider.family<ECPMConfig, ({String adNetwork, String adType})>(
        (ref, params) async {
  final analyticsService = ref.watch(analyticsServiceProvider);
  return analyticsService.getECPMConfig(params.adNetwork, params.adType);
});

// Proveedor para filtrar los datos según los filtros seleccionados
final filteredAnalyticsDataProvider =
    FutureProvider<AnalyticsData>((ref) async {
  final data = await ref.watch(analyticsDataProvider.future);
  final selectedNetwork = ref.watch(selectedNetworkProvider);
  final selectedCountry = ref.watch(selectedCountryProvider);

  return AnalyticsService.filterAnalyticsData(
    data,
    network: selectedNetwork,
    country: selectedCountry,
  );
});

// Provider para datos de analítica del usuario actual
final userAnalyticsDataProvider =
    FutureProvider<UserAnalyticsData>((ref) async {
  final dateRange = ref.watch(dateRangeProvider);
  final selectedNetwork = ref.watch(selectedNetworkProvider);
  final progressNotifier = ref.read(analyticsLoadingProgressProvider.notifier);

  // Configurar el callback de progreso
  final progressCallback = (int current, int total) {
    progressNotifier.state = LoadingProgress(current: current, total: total);
  };

  final userId = FirebaseService.currentUserId;
  if (userId == null) {
    throw Exception('Usuario no autenticado');
  }

  return await AnalyticsService.fetchUserAnalyticsData(
    userId: userId,
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
    networkFilter: selectedNetwork,
    onProgress: progressCallback,
  );
});
