import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ecpm_config.dart';
import '../services/analytics_service.dart';

// Proveedor para el servicio de analytics
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return AnalyticsService();
});

// Proveedor para obtener la configuración de eCPM para una red y tipo específicos
final ecpmConfigProvider = FutureProvider.family<ECPMConfig, ({String adNetwork, String adType})>((ref, params) async {
  final analyticsService = ref.watch(analyticsServiceProvider);
  return await analyticsService.getECPMConfig(params.adNetwork, params.adType);
});

// Proveedor para obtener todas las configuraciones de eCPM
final allEcpmConfigsProvider = FutureProvider<List<ECPMConfig>>((ref) async {
  final analyticsService = ref.watch(analyticsServiceProvider);
  final adNetworks = ['AdMob', 'Unity', 'Meta'];
  final adTypes = ['reward']; // Por ahora solo usamos reward
  
  List<ECPMConfig> configs = [];
  
  for (final network in adNetworks) {
    for (final type in adTypes) {
      final config = await analyticsService.getECPMConfig(network, type);
      configs.add(config);
    }
  }
  
  return configs;
});

// Proveedor para actualizar la configuración de eCPM
final updateEcpmConfigProvider = FutureProvider.family<bool, ECPMConfig>((ref, config) async {
  try {
    final analyticsService = ref.watch(analyticsServiceProvider);
    await analyticsService.updateECPMConfig(config);
    
    // Invalidar los proveedores para refrescar los datos
    ref.invalidate(allEcpmConfigsProvider);
    ref.invalidate(ecpmConfigProvider);
    
    return true;
  } catch (e) {
    print('Error al actualizar configuración de eCPM: $e');
    return false;
  }
});

// Proveedor para los países disponibles
final availableCountriesProvider = Provider<List<String>>((ref) {
  return [
    'US', 'MX', 'BR', 'AR', 'CO', 'CL', 'PE', 'ES',
    'GB', 'DE', 'FR', 'IT', 'JP', 'KR', 'AU', 'CA',
    'IN', 'RU', 'CN', 'ZA'
  ];
});
