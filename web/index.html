<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Dashboard for PPAnalitics Reward game ad performance.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="PP Analytics">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>PP Analytics Reward</title>
  <link rel="manifest" href="manifest.json">

  <script src="flutter.js" defer></script>
  <style>
    body {
      background-color: #121212;
      color: white;
      font-family: Arial, sans-serif;
      margin: 0;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .loader {
      border: 8px solid #333;
      border-radius: 50%;
      border-top: 8px solid #2196F3;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .message {
      font-size: 18px;
      margin-top: 16px;
    }
  </style>
</head>
<body>
  <div class="loading" id="loading">
    <div class="loader"></div>
    <div class="message">Cargando PP Analytics...</div>
  </div>

  <script>
    window.addEventListener('load', function() {
      // Cargar la aplicación Flutter
      var script = document.createElement('script');
      script.src = 'main.dart.js';
      script.type = 'application/javascript';
      document.body.appendChild(script);

      // Ocultar el indicador de carga cuando la aplicación esté lista
      window.addEventListener('flutter-first-frame', function() {
        var loadingElement = document.getElementById('loading');
        if (loadingElement) {
          loadingElement.style.display = 'none';
        }
      });
    });
  </script>
</body>
</html>