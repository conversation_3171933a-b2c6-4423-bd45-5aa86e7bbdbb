{"indexes": [{"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "country", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "country", "order": "ASCENDING"}, {"fieldPath": "createAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createAt", "order": "DESCENDING"}]}, {"collectionGroup": "withdrawals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "requestDate", "order": "DESCENDING"}]}, {"collectionGroup": "withdrawals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "requestDate", "order": "DESCENDING"}]}, {"collectionGroup": "withdrawals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "processedDate", "order": "DESCENDING"}]}, {"collectionGroup": "withdrawals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "requestDate", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}]}, {"collectionGroup": "withdrawals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "processedDate", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}]}, {"collectionGroup": "records", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "records", "queryScope": "COLLECTION", "fields": [{"fieldPath": "completed", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}], "fieldOverrides": []}