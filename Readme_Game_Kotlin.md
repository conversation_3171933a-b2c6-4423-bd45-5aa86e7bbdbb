# Guía de Implementación para Piñata Party (Kotlin)

Esta guía detalla la integración del juego Piñata Party (desarrollado en Kotlin con Clean Architecture) con el sistema web de análisis PP Analytics Reward. Proporciona la estructura de datos, modelos y ejemplos de código necesarios para asegurar la compatibilidad entre ambos sistemas, siguiendo los principios de Clean Architecture para mantener un código modular, testeable y mantenible.

## Índice
1. [Estructura del Modelo de Usuario](#estructura-del-modelo-de-usuario)
2. [Campos a Enviar desde el Juego](#campos-a-enviar-desde-el-juego)
3. [Estructura de Datos para Impresiones de Anuncios](#estructura-de-datos-para-impresiones-de-anuncios)
4. [Colecciones de Firebase](#colecciones-de-firebase)
5. [Ejemplos de Código en Kotlin](#ejemplos-de-código-en-kotlin)
6. [Flujo de Datos](#flujo-de-datos)
7. [Diagrama de Integración](#diagrama-de-integración)
8. [Buenas Prácticas con Clean Architecture](#buenas-prácticas-con-clean-architecture)

## Estructura del Modelo de Usuario

A continuación se presenta una comparación entre los modelos utilizados en el sistema web (Flutter) y los modelos que deben implementarse en el juego (Kotlin con Clean Architecture).

### Comparación de Modelos

| Sistema Web (Flutter) | Juego (Kotlin) | Descripción |
|----------------------|----------------|-------------|
| `UserManagement` / `UserProfile` | `UserEntity` | Modelo principal de usuario |
| `AdImpression` | `AdImpressionEntity` | Modelo para impresiones de anuncios |
| `DailyAdCounter` | `DailyAdCounterEntity` | Modelo para contadores diarios |

### Modelos en el Sistema Web (Flutter)

#### UserManagement (Completo)
```dart
class UserManagement {
  final String id;                  // ID de Firebase Auth
  final String email;               // Email del usuario
  final String? phoneNumber;        // Número de teléfono (opcional)
  final bool isActive;              // Estado del usuario
  final bool isEmailVerified;       // Si el correo ha sido verificado
  final bool isPhoneVerified;       // Si el teléfono ha sido verificado
  final DateTime? createAt;         // Fecha de registro
  final DateTime? lastUpdateAt;     // Última actualización
  final DateTime? lastLoginAt;      // Último inicio de sesión
  final String country;             // País del usuario
  final double adEarnings;          // Ganancias totales acumuladas
  final double availableAdEarnings; // Ganancias disponibles para retirar
  final int completedRewardAds;     // Total de anuncios completados
  final int processedRewardAds;     // Anuncios procesados para ganancias
  final String? referralCode;       // Código de referido
  final int referralCount;          // Número de usuarios referidos
  final String? referredBy;         // Quién refirió a este usuario
  final double totalReward;         // Recompensas por referidos
}
```

#### AdImpression (Completo)
```dart
class AdImpression {
  final String id;
  final String userId;
  final DateTime timestamp;
  final String adType;         // Siempre 'reward'
  final String adNetwork;      // Red publicitaria (AdMob, Unity, Meta)
  final bool completed;        // Si el anuncio fue completado
  final String country;        // País del usuario
  final String appVersion;     // Versión de la aplicación
  final String? rewardType;    // Tipo de recompensa (opcional)
  final int? rewardAmount;     // Cantidad de recompensa (opcional)
  final double? revenue;       // Ingresos calculados (sistema web)
  final double? valueUSD;      // Valor monetario del anuncio en USD (proporcionado por AdMob)
  final String? valuePrecision; // Precisión del valor (UNKNOWN, ESTIMATED, PRECISE, etc.)
}
```

### Modelos para el Juego (Kotlin con Clean Architecture)

#### Domain Layer (Entidades)

```kotlin
// Entidad de Usuario (Domain Layer)
data class UserEntity(
    val id: String,                 // ID de Firebase Auth
    val email: String,              // Email del usuario (obligatorio)
    val country: String,            // País del usuario (código ISO, ej: "MX", "US")
    val createAt: Date? = null,     // Fecha de creación
    val lastLoginAt: Date? = null,  // Última fecha de inicio de sesión
    val completedRewardAds: Int = 0 // Total de anuncios completados
)
```

```kotlin
// Entidad de Impresión de Anuncio (Domain Layer)
data class AdImpressionEntity(
    val id: String = "",            // ID generado por Firestore (vacío al crear)
    val userId: String,             // ID del usuario
    val timestamp: Date = Date(),   // Timestamp de la impresión
    val adNetwork: String,          // Red publicitaria (AdMob, Unity, Meta)
    val adType: String = "reward",  // Tipo de anuncio (siempre "reward")
    val completed: Boolean = true,  // Si el anuncio fue completado (siempre true)
    val country: String,            // País del usuario
    val appVersion: String,         // Versión de la app
    val rewardType: String? = null, // Tipo de recompensa (opcional)
    val rewardAmount: Int? = null,  // Cantidad de recompensa (opcional)
    val valueUSD: Double? = null,   // Valor monetario del anuncio en USD (proporcionado por AdMob)
    val valuePrecision: String? = null // Precisión del valor (UNKNOWN, ESTIMATED, PRECISE, etc.)
)
```

```kotlin
// Entidad de Contador Diario (Domain Layer)
data class DailyAdCounterEntity(
    val userId: String,             // ID del usuario
    val date: Date,                 // Fecha actual
    val completedRewardAds: Int     // Número de anuncios completados
)
```

#### Data Layer (Modelos de Datos)

```kotlin
// Modelo de Usuario para Firestore (Data Layer)
data class UserModel(
    val id: String,
    val email: String,
    val country: String,
    val createAt: Date? = null,
    val lastLoginAt: Date? = null,
    val isActive: Boolean = true,
    val isEmailVerified: Boolean = false,
    val isPhoneVerified: Boolean = false,
    val completedRewardAds: Int = 0,
    val processedRewardAds: Int = 0
) {
    // Convertir a entidad de dominio
    fun toEntity(): UserEntity = UserEntity(
        id = id,
        email = email,
        country = country,
        createAt = createAt,
        lastLoginAt = lastLoginAt,
        completedRewardAds = completedRewardAds
    )

    // Convertir a mapa para Firestore
    fun toMap(): Map<String, Any?> = mapOf(
        "email" to email,
        "country" to country,
        "createAt" to createAt,
        "lastLoginAt" to lastLoginAt,
        "isActive" to isActive,
        "isEmailVerified" to isEmailVerified,
        "isPhoneVerified" to isPhoneVerified,
        "completedRewardAds" to completedRewardAds,
        "processedRewardAds" to processedRewardAds
    )

    companion object {
        // Crear desde entidad de dominio
        fun fromEntity(entity: UserEntity): UserModel = UserModel(
            id = entity.id,
            email = entity.email,
            country = entity.country,
            createAt = entity.createAt,
            lastLoginAt = entity.lastLoginAt,
            completedRewardAds = entity.completedRewardAds
        )
    }
}
```

```kotlin
// Modelo de Impresión de Anuncio para Firestore (Data Layer)
data class AdImpressionModel(
    val id: String = "",
    val userId: String,
    val timestamp: Date = Date(),
    val adNetwork: String,
    val adType: String = "reward",
    val completed: Boolean = true,
    val country: String,
    val appVersion: String,
    val rewardType: String? = null,
    val rewardAmount: Int? = null,
    val valueUSD: Double? = null,
    val valuePrecision: String? = null
) {
    // Convertir a entidad de dominio
    fun toEntity(): AdImpressionEntity = AdImpressionEntity(
        id = id,
        userId = userId,
        timestamp = timestamp,
        adNetwork = adNetwork,
        adType = adType,
        completed = completed,
        country = country,
        appVersion = appVersion,
        rewardType = rewardType,
        rewardAmount = rewardAmount,
        valueUSD = valueUSD,
        valuePrecision = valuePrecision
    )

    // Convertir a mapa para Firestore
    fun toMap(): Map<String, Any?> = mapOf(
        "userId" to userId,
        "timestamp" to timestamp,
        "adNetwork" to adNetwork,
        "adType" to adType,
        "completed" to completed,
        "country" to country,
        "appVersion" to appVersion,
        "rewardType" to rewardType,
        "rewardAmount" to rewardAmount,
        "valueUSD" to valueUSD,
        "valuePrecision" to valuePrecision
    )

    companion object {
        // Crear desde entidad de dominio
        fun fromEntity(entity: AdImpressionEntity): AdImpressionModel = AdImpressionModel(
            id = entity.id,
            userId = entity.userId,
            timestamp = entity.timestamp,
            adNetwork = entity.adNetwork,
            adType = entity.adType,
            completed = entity.completed,
            country = entity.country,
            appVersion = entity.appVersion,
            rewardType = entity.rewardType,
            rewardAmount = entity.rewardAmount,
            valueUSD = entity.valueUSD,
            valuePrecision = entity.valuePrecision
        )
    }
}
```

## Campos a Enviar desde el Juego

### Para Registro/Login de Usuario

| Campo | Tipo | Descripción | Obligatorio |
|-------|------|-------------|-------------|
| `email` | String | Correo electrónico del usuario | Sí |
| `country` | String | Código ISO del país (ej: "MX", "US") | Sí |
| `createAt` | Timestamp | Fecha de creación (usar serverTimestamp) | Sí |
| `lastLoginAt` | Timestamp | Última fecha de inicio de sesión | Sí |

### Para Impresiones de Anuncios

| Campo | Tipo | Descripción | Obligatorio |
|-------|------|-------------|-------------|
| `userId` | String | ID del usuario en Firebase Auth | Sí |
| `timestamp` | Timestamp | Momento de la impresión (usar serverTimestamp) | Sí |
| `adNetwork` | String | Red publicitaria (AdMob, Unity, Meta) | Sí |
| `adType` | String | Tipo de anuncio (siempre "reward") | Sí |
| `completed` | Boolean | Si el anuncio fue completado (siempre true) | Sí |
| `country` | String | Código ISO del país del usuario | Sí |
| `appVersion` | String | Versión de la aplicación | Sí |
| `valueUSD` | Double | Valor monetario del anuncio en USD (proporcionado por AdMob) | No |
| `valuePrecision` | String | Precisión del valor (UNKNOWN, ESTIMATED, PRECISE, etc.) | No |

## Estructura de Datos para Impresiones de Anuncios

Cada vez que un usuario completa la visualización de un anuncio de recompensa, se deben realizar tres operaciones:

1. **Registrar la impresión del anuncio** en la colección de impresiones
2. **Actualizar el contador diario** en la subcarpeta de historial del usuario
3. **Incrementar el contador total** en el documento del usuario

## Colecciones de Firebase

### Colecciones Principales

| Colección | Descripción | Uso |
|-----------|-------------|-----|
| `projects/pinataparty/users/{userId}` | Datos principales del usuario | Registro/login y actualización de contadores |
| `projects/pinataparty/adAnalytics/impressions/records/{impressionId}` | Registro de impresiones de anuncios | Cada vez que se completa un anuncio |
| `projects/pinataparty/users/{userId}/adHistory/{year}/{month}/{day}` | Contador diario de anuncios | Actualización diaria de contadores |

### Estructura Jerárquica

```
projects/
  - pinataparty/
    - users/
      - {userId}/
        - email: string
        - country: string
        - createAt: timestamp
        - lastLoginAt: timestamp
        - isActive: boolean
        - completedRewardAds: number
        - adHistory/
          - {year}/
            - {month}/
              - {day}/
                - completedRewardAds: number
                - timestamp: timestamp

    - adAnalytics/
      - impressions/
        - records/
          - {impressionId}/
            - userId: string
            - timestamp: timestamp
            - adNetwork: string
            - adType: string (siempre "reward")
            - completed: boolean (siempre true)
            - country: string
            - appVersion: string
```

## Ejemplos de Código en Kotlin con Clean Architecture e Inyección de Dependencias

### Configuración del Módulo de Inyección de Dependencias (Hilt)

```kotlin
// Configuración de Hilt en el archivo Application
@HiltAndroidApp
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        // Inicializar Firebase
        FirebaseApp.initializeApp(this)
    }
}

// Módulo de Hilt para proveer dependencias
@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideFirebaseFirestore(): FirebaseFirestore {
        val settings = FirebaseFirestoreSettings.Builder()
            .setPersistenceEnabled(true)
            .build()
        return FirebaseFirestore.getInstance().apply {
            firestoreSettings = settings
        }
    }

    @Provides
    @Singleton
    fun provideFirebaseAuth(): FirebaseAuth {
        return FirebaseAuth.getInstance()
    }

    @Provides
    @Singleton
    fun provideUserRepository(
        firestore: FirebaseFirestore,
        auth: FirebaseAuth
    ): UserRepository {
        return UserRepositoryImpl(firestore, auth)
    }

    @Provides
    @Singleton
    fun provideAdRepository(
        firestore: FirebaseFirestore,
        auth: FirebaseAuth
    ): AdRepository {
        return AdRepositoryImpl(firestore, auth)
    }
}
```

### Interfaces de Repositorios (Domain Layer)

```kotlin
// Interfaz de repositorio de usuarios (Domain Layer)
interface UserRepository {
    suspend fun getCurrentUser(): UserEntity?
    suspend fun registerOrUpdateUser(email: String, country: String): Result<UserEntity>
    suspend fun getUserById(userId: String): Result<UserEntity>
    suspend fun updateUserAdCount(userId: String, increment: Int = 1): Result<Boolean>
    suspend fun getUserCountry(): String
    suspend fun getAppVersion(): String
}

// Interfaz de repositorio de anuncios (Domain Layer)
interface AdRepository {
    suspend fun recordImpression(impression: AdImpressionEntity): Result<Boolean>
    suspend fun updateDailyCounter(userId: String, date: Date, increment: Int = 1): Result<Boolean>
}

### Implementación de Repositorios (Data Layer)

```kotlin
// Implementación del repositorio de usuarios (Data Layer)
@Singleton
class UserRepositoryImpl @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val auth: FirebaseAuth,
    private val context: Context
) : UserRepository {

    override suspend fun getCurrentUser(): UserEntity? {
        val firebaseUser = auth.currentUser ?: return null
        return getUserById(firebaseUser.uid).getOrNull()
    }

    override suspend fun registerOrUpdateUser(email: String, country: String): Result<UserEntity> = withContext(Dispatchers.IO) {
        try {
            val firebaseUser = auth.currentUser
                ?: return@withContext Result.failure(Exception("Usuario no autenticado"))

            val userId = firebaseUser.uid
            val userModel = UserModel(
                id = userId,
                email = email,
                country = country,
                createAt = Date(),
                lastLoginAt = Date()
            )

            // Usar transacción para garantizar atomicidad
            firestore.runTransaction { transaction ->
                val userRef = firestore.collection("projects/pinataparty/users").document(userId)
                val existingDoc = transaction.get(userRef)

                if (existingDoc.exists()) {
                    // Actualizar solo lastLoginAt si el usuario ya existe
                    transaction.update(userRef, mapOf(
                        "lastLoginAt" to FieldValue.serverTimestamp()
                    ))
                } else {
                    // Crear nuevo usuario
                    transaction.set(userRef, userModel.toMap(), SetOptions.merge())
                }
            }.await()

            return@withContext Result.success(userModel.toEntity())
        } catch (e: Exception) {
            Log.e("UserRepository", "Error al registrar/actualizar usuario", e)
            return@withContext Result.failure(e)
        }
    }

    override suspend fun getUserById(userId: String): Result<UserEntity> = withContext(Dispatchers.IO) {
        try {
            val docSnapshot = firestore.collection("projects/pinataparty/users")
                .document(userId)
                .get()
                .await()

            if (!docSnapshot.exists()) {
                return@withContext Result.failure(Exception("Usuario no encontrado"))
            }

            val data = docSnapshot.data ?: return@withContext Result.failure(Exception("Datos de usuario vacíos"))

            val userModel = UserModel(
                id = userId,
                email = data["email"] as? String ?: "",
                country = data["country"] as? String ?: "",
                createAt = (data["createAt"] as? Timestamp)?.toDate(),
                lastLoginAt = (data["lastLoginAt"] as? Timestamp)?.toDate(),
                isActive = data["isActive"] as? Boolean ?: true,
                isEmailVerified = data["isEmailVerified"] as? Boolean ?: false,
                isPhoneVerified = data["isPhoneVerified"] as? Boolean ?: false,
                completedRewardAds = (data["completedRewardAds"] as? Number)?.toInt() ?: 0,
                processedRewardAds = (data["processedRewardAds"] as? Number)?.toInt() ?: 0
            )

            return@withContext Result.success(userModel.toEntity())
        } catch (e: Exception) {
            Log.e("UserRepository", "Error al obtener usuario", e)
            return@withContext Result.failure(e)
        }
    }

    override suspend fun updateUserAdCount(userId: String, increment: Int): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val userRef = firestore.collection("projects/pinataparty/users").document(userId)

            firestore.runTransaction { transaction ->
                transaction.update(userRef, mapOf(
                    "completedRewardAds" to FieldValue.increment(increment.toLong()),
                    "lastUpdateAt" to FieldValue.serverTimestamp()
                ))
            }.await()

            return@withContext Result.success(true)
        } catch (e: Exception) {
            Log.e("UserRepository", "Error al actualizar contador de anuncios", e)
            return@withContext Result.failure(e)
        }
    }

    override suspend fun getUserCountry(): String {
        // Obtener país del dispositivo (implementación simplificada)
        return try {
            val locale = context.resources.configuration.locales[0]
            locale.country
        } catch (e: Exception) {
            "US" // País por defecto
        }
    }

    override suspend fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName
        } catch (e: Exception) {
            "1.0.0" // Versión por defecto
        }
    }
}
```

```kotlin
// Implementación del repositorio de anuncios (Data Layer)
@Singleton
class AdRepositoryImpl @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val auth: FirebaseAuth
) : AdRepository {

    override suspend fun recordImpression(impression: AdImpressionEntity): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val batch = firestore.batch()

            // 1. Registrar impresión
            val impressionModel = AdImpressionModel.fromEntity(impression)
            val impressionRef = firestore.collection("projects/pinataparty/adAnalytics/impressions/records").document()
            batch.set(impressionRef, impressionModel.toMap())

            // 2. Actualizar contador diario
            val now = Calendar.getInstance()
            val year = now.get(Calendar.YEAR).toString()
            val month = String.format("%02d", now.get(Calendar.MONTH) + 1)
            val day = String.format("%02d", now.get(Calendar.DAY_OF_MONTH))

            val dailyRef = firestore.document(
                "projects/pinataparty/users/${impression.userId}/adHistory/$year/$month/$day"
            )

            batch.set(dailyRef, mapOf(
                "completedRewardAds" to FieldValue.increment(1),
                "timestamp" to FieldValue.serverTimestamp()
            ), SetOptions.merge())

            // 3. Actualizar contador total
            val userRef = firestore.collection("projects/pinataparty/users").document(impression.userId)
            batch.update(userRef, mapOf(
                "completedRewardAds" to FieldValue.increment(1),
                "lastUpdateAt" to FieldValue.serverTimestamp()
            ))

            // Ejecutar todas las operaciones en un lote
            batch.commit().await()

            return@withContext Result.success(true)
        } catch (e: Exception) {
            Log.e("AdRepository", "Error al registrar impresión", e)
            return@withContext Result.failure(e)
        }
    }

    override suspend fun updateDailyCounter(userId: String, date: Date, increment: Int): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val calendar = Calendar.getInstance().apply { time = date }
            val year = calendar.get(Calendar.YEAR).toString()
            val month = String.format("%02d", calendar.get(Calendar.MONTH) + 1)
            val day = String.format("%02d", calendar.get(Calendar.DAY_OF_MONTH))

            val dailyRef = firestore.document(
                "projects/pinataparty/users/$userId/adHistory/$year/$month/$day"
            )

            dailyRef.set(mapOf(
                "completedRewardAds" to FieldValue.increment(increment.toLong()),
                "timestamp" to FieldValue.serverTimestamp()
            ), SetOptions.merge()).await()

            return@withContext Result.success(true)
        } catch (e: Exception) {
            Log.e("AdRepository", "Error al actualizar contador diario", e)
            return@withContext Result.failure(e)
        }
    }
}
```

### Casos de Uso (Domain Layer)

```kotlin
// Caso de uso para registrar usuario
class RegisterUserUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    suspend operator fun invoke(email: String, password: String): Result<UserEntity> {
        return try {
            // Obtener país del dispositivo
            val country = userRepository.getUserCountry()

            // Registrar o actualizar usuario
            userRepository.registerOrUpdateUser(email, country)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

// Caso de uso para registrar impresión de anuncio
class RecordAdImpressionUseCase @Inject constructor(
    private val adRepository: AdRepository,
    private val userRepository: UserRepository
) {
    suspend operator fun invoke(
        adNetwork: String,
        valueUSD: Double? = null,
        valuePrecision: String? = null
    ): Result<Boolean> {
        try {
            // Obtener usuario actual
            val currentUser = userRepository.getCurrentUser()
                ?: return Result.failure(Exception("Usuario no autenticado"))

            // Obtener información del dispositivo
            val country = userRepository.getUserCountry()
            val appVersion = userRepository.getAppVersion()

            // Crear entidad
            val impression = AdImpressionEntity(
                userId = currentUser.id,
                adNetwork = adNetwork,
                country = country,
                appVersion = appVersion,
                valueUSD = valueUSD,
                valuePrecision = valuePrecision
            )

            // Registrar impresión
            return adRepository.recordImpression(impression)
        } catch (e: Exception) {
            return Result.failure(e)
        }
    }
}

// Caso de uso para obtener perfil de usuario
class GetUserProfileUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    suspend operator fun invoke(): Result<UserEntity> {
        return try {
            val currentUser = userRepository.getCurrentUser()
                ?: return Result.failure(Exception("Usuario no autenticado"))

            Result.success(currentUser)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

### ViewModels (Presentation Layer)

```kotlin
@HiltViewModel
class AdViewModel @Inject constructor(
    private val recordAdImpressionUseCase: RecordAdImpressionUseCase
) : ViewModel() {

    private val _adRecordState = MutableStateFlow<AdRecordState>(AdRecordState.Idle)
    val adRecordState: StateFlow<AdRecordState> = _adRecordState

    fun recordAdImpression(
        adNetwork: String,
        valueUSD: Double? = null,
        valuePrecision: String? = null
    ) {
        viewModelScope.launch {
            _adRecordState.value = AdRecordState.Loading

            val result = recordAdImpressionUseCase(
                adNetwork = adNetwork,
                valueUSD = valueUSD,
                valuePrecision = valuePrecision
            )

            _adRecordState.value = if (result.isSuccess) {
                AdRecordState.Success
            } else {
                AdRecordState.Error(result.exceptionOrNull()?.message ?: "Error desconocido")
            }
        }
    }

    sealed class AdRecordState {
        object Idle : AdRecordState()
        object Loading : AdRecordState()
        object Success : AdRecordState()
        data class Error(val message: String) : AdRecordState()
    }
}

@HiltViewModel
class UserViewModel @Inject constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase
) : ViewModel() {

    private val _userState = MutableStateFlow<UserState>(UserState.Loading)
    val userState: StateFlow<UserState> = _userState

    init {
        loadUserProfile()
    }

    fun loadUserProfile() {
        viewModelScope.launch {
            _userState.value = UserState.Loading

            val result = getUserProfileUseCase()

            _userState.value = if (result.isSuccess) {
                UserState.Success(result.getOrNull()!!)
            } else {
                UserState.Error(result.exceptionOrNull()?.message ?: "Error desconocido")
            }
        }
    }

    sealed class UserState {
        object Loading : UserState()
        data class Success(val user: UserEntity) : UserState()
        data class Error(val message: String) : UserState()
    }
}
```

### Jetpack Compose UI (Presentation Layer)

```kotlin
@Composable
fun AdRewardScreen(
    viewModel: AdViewModel = hiltViewModel(),
    onNavigateBack: () -> Unit
) {
    val adState by viewModel.adRecordState.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Anuncio de Recompensa",
            style = MaterialTheme.typography.h5,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(32.dp))

        when (val state = adState) {
            is AdViewModel.AdRecordState.Idle -> {
                Button(
                    onClick = {
                        // Ejemplo de cómo pasar los valores de AdMob
                        viewModel.recordAdImpression(
                            adNetwork = "AdMob",
                            valueUSD = 0.02, // Valor proporcionado por AdMob
                            valuePrecision = "ESTIMATED" // Precisión proporcionada por AdMob
                        )
                    },
                    modifier = Modifier.fillMaxWidth(0.7f)
                ) {
                    Text("Ver Anuncio")
                }
            }
            is AdViewModel.AdRecordState.Loading -> {
                CircularProgressIndicator()
            }
            is AdViewModel.AdRecordState.Success -> {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = Color.Green,
                    modifier = Modifier.size(64.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "¡Recompensa obtenida!",
                    style = MaterialTheme.typography.h6
                )

                Spacer(modifier = Modifier.height(32.dp))

                Button(
                    onClick = onNavigateBack,
                    modifier = Modifier.fillMaxWidth(0.7f)
                ) {
                    Text("Continuar")
                }
            }
            is AdViewModel.AdRecordState.Error -> {
                Icon(
                    imageVector = Icons.Default.Error,
                    contentDescription = null,
                    tint = Color.Red,
                    modifier = Modifier.size(64.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "Error: ${state.message}",
                    style = MaterialTheme.typography.body1,
                    color = Color.Red
                )

                Spacer(modifier = Modifier.height(32.dp))

                Button(
                    onClick = {
                        viewModel.recordAdImpression(
                            adNetwork = "AdMob",
                            valueUSD = 0.02,
                            valuePrecision = "ESTIMATED"
                        )
                    },
                    modifier = Modifier.fillMaxWidth(0.7f)
                ) {
                    Text("Reintentar")
                }
            }
        }
    }
}

@Composable
fun UserProfileScreen(
    viewModel: UserViewModel = hiltViewModel()
) {
    val userState by viewModel.userState.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "Perfil de Usuario",
            style = MaterialTheme.typography.h5,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(32.dp))

        when (val state = userState) {
            is UserViewModel.UserState.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            is UserViewModel.UserState.Success -> {
                val user = state.user

                ProfileItem(title = "Email", value = user.email)
                ProfileItem(title = "País", value = user.country)
                ProfileItem(
                    title = "Anuncios Completados",
                    value = user.completedRewardAds.toString()
                )

                Spacer(modifier = Modifier.height(32.dp))

                Button(
                    onClick = { viewModel.loadUserProfile() },
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                ) {
                    Text("Actualizar Perfil")
                }
            }
            is UserViewModel.UserState.Error -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            tint = Color.Red,
                            modifier = Modifier.size(64.dp)
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = "Error: ${state.message}",
                            style = MaterialTheme.typography.body1,
                            color = Color.Red
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Button(onClick = { viewModel.loadUserProfile() }) {
                            Text("Reintentar")
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ProfileItem(title: String, value: String) {
    Column(modifier = Modifier.padding(vertical = 8.dp)) {
        Text(
            text = title,
            style = MaterialTheme.typography.caption,
            color = Color.Gray
        )

        Text(
            text = value,
            style = MaterialTheme.typography.body1
        )

        Divider(modifier = Modifier.padding(top = 8.dp))
    }
}
```

## Flujo de Datos

### Registro/Login de Usuario

1. El usuario se registra o inicia sesión en el juego
2. El juego obtiene el ID de usuario de Firebase Auth
3. El juego registra/actualiza los datos del usuario en `projects/pinataparty/users/{userId}`
4. El sistema web lee estos datos para mostrar información del usuario

### Visualización de Anuncio

1. El usuario completa la visualización de un anuncio de recompensa
2. El juego registra la impresión en `projects/pinataparty/adAnalytics/impressions/records`
3. El juego actualiza el contador diario en `projects/pinataparty/users/{userId}/adHistory/{year}/{month}/{day}`
4. El juego incrementa el contador total en `projects/pinataparty/users/{userId}`
5. El sistema web lee estos datos para calcular ganancias y mostrar estadísticas

## Diagrama de Integración

```
┌─────────────┐                  ┌─────────────┐                  ┌─────────────┐
│             │                  │             │                  │             │
│  Piñata     │  1. Registro    │  Firebase   │  3. Lectura      │  Sistema    │
│  Party      │ ───────────────►│  Firestore  │◄─────────────────│  Web        │
│  (Kotlin)   │                  │             │                  │  (Flutter)  │
│             │  2. Impresiones  │             │  4. Cálculo      │             │
│             │ ───────────────►│             │◄─────────────────│             │
└─────────────┘                  └─────────────┘                  └─────────────┘
```

---

**Nota importante**: Esta implementación asegura que el juego Piñata Party (Kotlin) sea compatible con el sistema web PP Analytics Reward (Flutter), permitiendo un flujo de datos correcto y el cálculo adecuado de ganancias para los usuarios.

## Buenas Prácticas con Clean Architecture, Inyección de Dependencias y Jetpack Compose

### Estructura de Capas en Clean Architecture

1. **Domain Layer (Capa de Dominio)**:
   - Contiene las entidades de negocio (UserEntity, AdImpressionEntity)
   - Define interfaces de repositorios (UserRepository, AdRepository)
   - Implementa casos de uso (RegisterUserUseCase, RecordAdImpressionUseCase)
   - No tiene dependencias de frameworks externos (Firebase, Android, etc.)
   - Es completamente independiente de la plataforma

2. **Data Layer (Capa de Datos)**:
   - Implementa los repositorios definidos en la capa de dominio (UserRepositoryImpl, AdRepositoryImpl)
   - Gestiona fuentes de datos (Firebase, Room, SharedPreferences, etc.)
   - Mapea datos entre entidades de dominio y modelos de datos (UserModel, AdImpressionModel)
   - Maneja la lógica de acceso a datos y la comunicación con APIs externas

3. **Presentation Layer (Capa de Presentación)**:
   - Implementa la interfaz de usuario con Jetpack Compose
   - Contiene ViewModels que consumen casos de uso
   - Maneja estados de UI y eventos de usuario
   - Implementa la navegación entre pantallas

### Principios de Clean Architecture

1. **Dependencias hacia adentro**:
   ```
   Presentation Layer (Compose, ViewModels) → Domain Layer (Casos de Uso, Entidades) ← Data Layer (Repositorios)
   ```

2. **Inversión de dependencias**:
   - La capa de dominio define interfaces (repositorios)
   - Las capas externas implementan estas interfaces
   - Se utiliza inyección de dependencias para proporcionar implementaciones concretas

3. **Entidades independientes**:
   - Las entidades de dominio no dependen de frameworks externos
   - No contienen anotaciones específicas de frameworks
   - Son simples clases de datos (data class en Kotlin)

### Inyección de Dependencias con Hilt

1. **Configuración básica**:
   - Añadir la anotación `@HiltAndroidApp` a la clase Application
   - Definir módulos con `@Module` y `@InstallIn`
   - Proporcionar dependencias con `@Provides` y `@Singleton`

2. **Inyección en componentes**:
   - Anotar ViewModels con `@HiltViewModel`
   - Inyectar dependencias con `@Inject constructor`
   - Obtener ViewModels en Composables con `hiltViewModel()`

3. **Alcance de dependencias**:
   - Usar `@Singleton` para dependencias a nivel de aplicación
   - Usar `@ActivityRetainedScoped` para dependencias que sobreviven a cambios de configuración
   - Usar `@ViewModelScoped` para dependencias limitadas al ciclo de vida del ViewModel

### Jetpack Compose para UI

1. **Arquitectura de UI**:
   - Utilizar el patrón unidireccional de datos (UDF)
   - Estado fluye desde ViewModel hacia Composables
   - Eventos fluyen desde Composables hacia ViewModel

2. **Composables sin estado vs. con estado**:
   - Crear Composables sin estado para reutilización (ej: `ProfileItem`)
   - Usar Composables con estado para gestionar pantallas completas (ej: `UserProfileScreen`)
   - Separar lógica de UI de lógica de negocio

3. **Gestión de estado**:
   - Usar `StateFlow` en ViewModels para exponer estados
   - Recoger estados en Composables con `collectAsState()`
   - Modelar estados como clases selladas para manejar diferentes casos (loading, success, error)

4. **Navegación**:
   - Implementar navegación con Navigation Compose
   - Definir rutas y argumentos de navegación
   - Manejar la navegación desde ViewModels a través de eventos

### Manejo de Errores

1. **Implementar reintentos automáticos**:
   ```kotlin
   // Función para reintentar operaciones fallidas
   suspend fun <T> retryIO(
       times: Int = 3,
       initialDelay: Long = 100,
       maxDelay: Long = 1000,
       factor: Double = 2.0,
       block: suspend () -> T
   ): T {
       var currentDelay = initialDelay
       repeat(times - 1) {
           try {
               return block()
           } catch (e: Exception) {
               Log.e("Firebase", "Error en operación, reintentando...", e)
           }
           delay(currentDelay)
           currentDelay = (currentDelay * factor).toLong().coerceAtMost(maxDelay)
       }
       return block() // última oportunidad
   }
   ```

2. **Almacenamiento en caché local para operaciones offline**:
   - Utilizar Room Database para almacenar impresiones de anuncios pendientes de sincronizar
   - Implementar un WorkManager para sincronizar datos cuando se recupere la conexión

3. **Validación de datos**:
   - Validar que los campos obligatorios no sean nulos o vacíos antes de enviar a Firebase
   - Verificar que el usuario esté autenticado antes de realizar operaciones

### Optimización de Rendimiento

1. **Uso de lotes (batches) para múltiples operaciones**:
   - Agrupar operaciones relacionadas en un solo lote como se muestra en los ejemplos
   - Reducir el número de llamadas a la red

2. **Implementar caché de datos**:
   - Almacenar localmente información del usuario para reducir lecturas de Firestore
   - Actualizar la caché cuando se reciban nuevos datos

3. **Monitoreo de cuotas y límites**:
   - Implementar contadores para monitorear el uso de cuotas de Firebase
   - Establecer límites locales para evitar exceder cuotas diarias

### Seguridad

1. **Nunca almacenar credenciales sensibles en el código**:
   - Utilizar variables de entorno o almacenamiento seguro para claves API
   - No incluir información sensible en logs o reportes de errores

2. **Validar autenticación antes de cada operación**:
   ```kotlin
   fun requireAuthentication(): FirebaseUser {
       return auth.currentUser ?: throw SecurityException("Usuario no autenticado")
   }
   ```

3. **Implementar timeout para operaciones de red**:
   ```kotlin
   suspend fun <T> withTimeout(timeMillis: Long, block: suspend () -> T): T {
       return kotlinx.coroutines.withTimeoutOrNull(timeMillis, block)
           ?: throw TimeoutException("La operación excedió el tiempo límite de $timeMillis ms")
   }
   ```

### Pruebas en Clean Architecture

1. **Pruebas Unitarias para Casos de Uso (Domain Layer)**:
   ```kotlin
   @Test
   fun `test caso de uso de registro de impresión de anuncio`() = runTest {
       // Arrange
       val mockAdRepository = mock(AdRepository::class.java)
       val mockUserRepository = mock(UserRepository::class.java)
       val useCase = RecordAdImpressionUseCase(mockAdRepository, mockUserRepository)

       // Mock de datos
       val user = UserEntity("user123", "<EMAIL>", "MX")
       whenever(mockUserRepository.getCurrentUser()).thenReturn(user)
       whenever(mockUserRepository.getUserCountry()).thenReturn("MX")
       whenever(mockUserRepository.getAppVersion()).thenReturn("1.0.0")

       val adImpressionCaptor = argumentCaptor<AdImpressionEntity>()
       whenever(mockAdRepository.recordImpression(adImpressionCaptor.capture()))
           .thenReturn(Result.success(true))

       // Act
       val result = useCase("AdMob")

       // Assert
       assertTrue(result.isSuccess)
       assertEquals("user123", adImpressionCaptor.firstValue.userId)
       assertEquals("AdMob", adImpressionCaptor.firstValue.adNetwork)
       assertEquals("MX", adImpressionCaptor.firstValue.country)
   }
   ```

2. **Pruebas para Repositorios (Data Layer)**:
   ```kotlin
   @Test
   fun `test implementación de repositorio de anuncios`() = runTest {
       // Arrange
       val mockFirebaseService = mock(FirebaseService::class.java)
       val repository = AdRepositoryImpl(mockFirebaseService)
       val impression = AdImpressionEntity("user123", "AdMob", "MX", "1.0.0")

       whenever(mockFirebaseService.recordAdImpressionAsync(
           eq("user123"), eq("AdMob"), eq("MX"), eq("1.0.0")
       )).thenReturn(Result.success(true))

       // Act
       val result = repository.recordImpression(impression)

       // Assert
       assertTrue(result.isSuccess)
       verify(mockFirebaseService).recordAdImpressionAsync(
           eq("user123"), eq("AdMob"), eq("MX"), eq("1.0.0")
       )
   }
   ```

3. **Pruebas para Presentadores (Presentation Layer)**:
   ```kotlin
   @Test
   fun `test presentador de anuncios`() = runTest {
       // Arrange
       val mockUseCase = mock(RecordAdImpressionUseCase::class.java)
       val mockView = mock(AdView::class.java)
       val presenter = AdPresenter(mockUseCase, mockView)

       whenever(mockUseCase.invoke("AdMob")).thenReturn(Result.success(true))

       // Act
       presenter.onAdCompleted("AdMob")

       // Assert - Verificar interacciones con la vista
       verify(mockView).showLoading()
       verify(mockView).showSuccess()
       verify(mockView).hideLoading()
       verify(mockView, never()).showError(any())
   }
   ```

4. **Configurar un entorno de pruebas con Firebase Emulator**:
   - Utilizar Firebase Emulator Suite para pruebas locales sin afectar datos reales
   - Configurar reglas de seguridad específicas para pruebas

5. **Pruebas de Integración End-to-End**:
   - Verificar el flujo completo desde la UI hasta Firebase
   - Comprobar que los datos se almacenen correctamente en todas las colecciones
   - Validar que los contadores se actualicen adecuadamente
