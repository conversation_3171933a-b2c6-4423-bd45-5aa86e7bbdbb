rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // REGLA PRINCIPAL: Permitir acceso a todas las colecciones por defecto
    // Esto asegura que otros proyectos no se vean afectados
    match /{document=**} {
      allow read, write: if true;
    }

    // REGLAS ESPECÍFICAS PARA PP ANALYTICS REWARD
    // Estas reglas tienen prioridad sobre la regla general para estas colecciones específicas

    // adAnalytics collection
    match /projects/pinataparty/adAnalytics/{document=**} {
      // Funciones helper específicas para esta colección
      function isAdmin() {
        return request.auth != null &&
               request.auth.token.email != null &&
               request.auth.token.email.matches(".*@pinataparty\\.com");
      }

      function isAuthenticated() {
        return request.auth != null;
      }

      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // users collection
    match /projects/pinataparty/users/{userId} {
      // Funciones helper específicas para esta colección
      function isAdmin() {
        return request.auth != null &&
               request.auth.token.email != null &&
               request.auth.token.email.matches(".*@pinataparty\\.com");
      }

      function isAuthenticated() {
        return request.auth != null;
      }

      function isOwner(userId) {
        return request.auth != null && request.auth.uid == userId;
      }

      allow read: if isAuthenticated() && (isAdmin() || isOwner(userId));
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isAdmin() || isOwner(userId));
      allow delete: if isAdmin();
    }

    // adHistory subcollection
    match /projects/pinataparty/users/{userId}/adHistory/{document} {
      // Funciones helper específicas para esta colección
      function isAdmin() {
        return request.auth != null &&
               request.auth.token.email != null &&
               request.auth.token.email.matches(".*@pinataparty\\.com");
      }

      function isAuthenticated() {
        return request.auth != null;
      }

      function isOwner(userId) {
        return request.auth != null && request.auth.uid == userId;
      }

      allow read: if isAuthenticated() && (isAdmin() || isOwner(userId));
      allow write: if isAuthenticated() && (isAdmin() || isOwner(userId));
    }

    // withdrawals collection
    match /projects/pinataparty/withdrawals/{document=**} {
      // Funciones helper específicas para esta colección
      function isAdmin() {
        return request.auth != null &&
               request.auth.token.email != null &&
               request.auth.token.email.matches(".*@pinataparty\\.com");
      }

      function isAuthenticated() {
        return request.auth != null;
      }

      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAdmin();
    }

    // adminSettings collection
    match /projects/pinataparty/adminSettings/{document=**} {
      // Funciones helper específicas para esta colección
      function isAdmin() {
        return request.auth != null &&
               request.auth.token.email != null &&
               request.auth.token.email.matches(".*@pinataparty\\.com");
      }

      function isAuthenticated() {
        return request.auth != null;
      }

      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // adminLogs collection
    match /projects/pinataparty/adminLogs/{document=**} {
      // Funciones helper específicas para esta colección
      function isAdmin() {
        return request.auth != null &&
               request.auth.token.email != null &&
               request.auth.token.email.matches(".*@pinataparty\\.com");
      }

      allow read: if isAdmin();
      allow write: if isAdmin();
    }
  }
}