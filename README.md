# PP Analytics Reward

Sistema de análisis y recompensas para usuarios de Piñata Party basado en visualización de anuncios.

## ⚠️ ADVERTENCIA IMPORTANTE

**NUNCA ELIMINAR** las siguientes colecciones existentes en Firebase:
- `projects`
- `pinataparty`
- `users`
- `referrals`
- `referralsCode`

Estas colecciones contienen datos de producción esenciales para el funcionamiento del juego y el sistema de recompensas. Su eliminación podría causar pérdida permanente de datos de usuarios y problemas en el sistema.

## Actualizaciones y Mejoras Recientes

### Corrección de Inconsistencias en la Interfaz de Usuario (Junio 2025)
- Corregido el problema con la fecha de registro que no se mostraba correctamente en la lista de usuarios.
- Unificado el manejo de fechas entre `UserManagement` y `UserProfile` para asegurar consistencia.
- Mejorado el formato de fecha para mostrar tanto la fecha como la hora (`dd/MM/yyyy HH:mm`).
- Implementada búsqueda de campos de fecha tanto con `createAt` como con `createdAt` para mayor robustez.
- Corregida la visualización de campos financieros para mostrar correctamente las ganancias disponibles.
- Optimizado el constructor `fromFirestore` para manejar correctamente valores nulos o faltantes.
- Mejorado el formato de moneda para mostrar 4 decimales, permitiendo visualizar correctamente valores pequeños como 0.002.

### Mejora del Dashboard de Administrador (Mayo 2025)
- Modificación del filtrado de rango de fechas para mostrar por defecto el día actual en lugar de mes a mes.
- Mejora visual del selector de fechas con indicador especial cuando se muestra solo el día actual.
- Adición de un botón para restablecer rápidamente al día actual cuando se ha seleccionado otro rango.
- Corrección de problemas con los FloatingActionButton agregando heroTags únicos a cada uno.
- Corrección del problema con la carga del historial en la pantalla de Retiros pendientes:
  - Modificado el método `getProcessedWithdrawals` para usar `requestDate` en lugar de `processedDate` para filtrar por fecha
  - Implementado un nuevo enfoque de filtrado en memoria para evitar problemas con índices compuestos en Firestore
  - Reemplazado el `FutureProvider.family` por un `StateNotifierProvider` para evitar bucles infinitos de consultas
  - Mejorado el manejo de fechas para asegurar que los filtros funcionen correctamente
  - Añadidos mensajes de diagnóstico detallados en los logs para facilitar la depuración
  - Mejorada la interfaz de usuario para mostrar un indicador especial cuando se muestra solo el día actual

### Optimización de la Interfaz de Usuario (Junio 2025)
- Mejora de la experiencia de usuario en la pantalla de Retiros pendientes con carga más rápida y fluida.
- Implementación de un sistema de caché para reducir las consultas a Firestore y mejorar el rendimiento.
- Optimización del rendimiento general de la aplicación con mejor gestión de estado.

### Optimización del Cálculo de Ganancias (Enero 2025)
- Implementación de un nuevo sistema para calcular ganancias solo para anuncios nuevos, evitando recalcular repetidamente.
- Adición del campo `processedRewardAds` para rastrear qué anuncios ya han sido procesados para cálculo de ganancias.
- Mejora del proceso de retiro para evitar cálculos redundantes de ganancias.
- Optimización del rendimiento al cargar el dashboard del usuario.
- Adición de campos para rastrear la fecha y monto del último retiro.

### Simplificación del Proceso de Retiros (Diciembre 2024)
- Eliminación del requisito de verificación de teléfono para realizar retiros.
- Simplificación de la pantalla de perfil para permitir agregar/modificar teléfono sin verificación.
- Actualización de la pantalla de retiros para mostrar solo advertencias sobre correo electrónico no verificado.
- Mejora del proceso de actualización de teléfono sin resetear el estado de verificación.

### Actualización de Despliegue (Diciembre 2024)
- Actualización del despliegue a Firebase Hosting en la URL https://ppanaliticreward.web.app/
- Documentación mejorada del proceso de despliegue en el README
- Optimización de los comandos de despliegue para actualizaciones parciales

### Decisión de Arquitectura: Autenticación Exclusiva por Email
- Se ha decidido utilizar **exclusivamente autenticación por email** para el sistema web.
- La autenticación por email está completamente implementada y funcionando correctamente.
- El número de teléfono se mantiene como un dato adicional del usuario sin ser utilizado para autenticación.
- Esta decisión simplifica la arquitectura y mejora la seguridad del sistema.

### Verificación de Contacto para Retiros (Noviembre-Diciembre 2024)
- Implementación de verificación obligatoria de correo electrónico para realizar retiros.
- Simplificación del proceso al eliminar el requisito de verificación de teléfono.
- Mejora de la pantalla de perfil con indicadores visuales del estado de verificación de correo.
- Autollenado de campos de contacto en solicitudes de retiro usando la información del usuario.
- Bloqueo de edición de información de contacto durante el proceso de retiro para mayor seguridad.
- Corrección de problemas de navegación entre pantallas de usuario (historial, retiros, perfil).

### Mejora de la Navegación y Sistema de Referidos (Octubre 2024)
- Implementación de pantallas dedicadas para "Retiros" e "Historial de retiros" accesibles desde el menú lateral.
- Agregado sistema de visualización de referidos en el dashboard del usuario (código de referido, amigos referidos, Gemas Dulces).
- Mejora del proceso de solicitud de retiro con opciones específicas: Recarga Móvil (Telcel, Movistar, AT&T) y Tarjeta de Regalo (Amazon, Google Play, iTunes).
- Corrección de las reglas de seguridad de Firestore para evitar conflictos con otros proyectos en la misma base de datos.
- Actualización de la documentación para reflejar los cambios en la estructura y flujo de datos.

### Mejora de la Interfaz de Usuario (Septiembre 2024)
- Simplificación de la interfaz de usuario para enfocarse en las ganancias y retiros.
- Eliminación de métricas técnicas relacionadas con anuncios para mejorar la experiencia del usuario.
- Implementación de una pantalla de perfil dedicada para editar información personal.
- Mejora del proceso de solicitud de retiro con validaciones y selección de método de pago.
- Despliegue a Firebase Hosting con URL personalizada: ppanaliticreward.web.app.

### Centralización de Datos de Prueba (Agosto 2024)
- Centralización de todos los métodos de generación de datos de prueba en `test_data_service.dart`.
- Eliminación de métodos mock dispersos en diferentes archivos para mejorar la mantenibilidad.
- Implementación de un sistema unificado para inicializar datos de prueba para todas las colecciones.
- Corrección de problemas de inconsistencia en el dashboard al refrescar o cerrar sesión.

### Reestructuración de Modelos (Julio 2024)
- Creación de una estructura clara para los modelos de datos que separa las responsabilidades entre el juego y el sistema web.
- Eliminación de campos redundantes como `deviceInfo` y simplificación de `adType` a siempre 'reward'.
- Implementación de un sistema unificado para el cálculo de ganancias basado en multiplicadores por país.
- Actualización de servicios para generar datos de prueba completos para todas las colecciones.

### Integración con Firebase
- Corrección de la configuración para acceder correctamente a la ruta de datos en Firestore.
- Implementación de carga eficiente de datos con paginación desde la colección 'projects/pinataparty/adAnalytics/impressions/records'.
- Manejo adecuado de autenticación para usuarios administradores y regulares.

### Visualización de Datos
- Implementación de dashboard interactivo con estadísticas clave como impresiones totales, tasa de finalización y ganancias.
- Visualización de datos por país y red publicitaria mediante gráficos de barras y progreso visual.
- Filtrado de datos por red publicitaria, país y rango de fechas.

### Interfaz de Usuario
- Diseño moderno con tarjetas de estadísticas y gráficos visuales.
- Menú lateral para navegación entre secciones.
- Modo oscuro por defecto con colores contrastados para mejor lectura de datos.
- Sistema de filtrado intuitivo en la parte superior de la pantalla.

## Estructura de Firebase Actualizada

### Índices de Firestore

El sistema requiere los siguientes índices compuestos en Firestore:

1. **Colección `users`**:
   - Campos: `country` (ASC), `isActive` (ASC), `createAt` (DESC)
   - Utilizado para: Filtrar usuarios por país y estado de activación

2. **Colección `withdrawals`**:
   - Campos: `status` (ASC), `requestDate` (DESC)
   - Utilizado para: Listar retiros pendientes

3. **Colección `withdrawals`**:
   - Campos: `userId` (ASC), `requestDate` (DESC)
   - Utilizado para: Listar retiros de un usuario específico

4. **Colección `withdrawals`**:
   - Campos: `status` (ASC), `processedDate` (DESC)
   - Utilizado para: Filtrar retiros procesados por estado y fecha

Estos índices están definidos en el archivo `firestore.indexes.json` y pueden ser desplegados con el comando:

```bash
firebase deploy --only firestore:indexes
```

### Estructura de Colecciones

```
projects/
  - pinataparty/
    - users/
      - {userId}/
        - email: string
        - country: string
        - createAt: timestamp
        - lastLoginAt: timestamp
        - adHistory/
          - {year}/
            - {month}/
              - {day}/
                - completedRewardAds: number
                - timestamp: timestamp

    - users/
      - {userId}/
        - email: string
        - phoneNumber: string (opcional)
        - isEmailVerified: boolean
        - isPhoneVerified: boolean
        - createAt: timestamp
        - lastUpdateAt: timestamp
        - lastLoginAt: timestamp
        - country: string
        - isActive: boolean
        - adEarnings: number
        - availableAdEarnings: number
        - completedRewardAds: number
        - referralCode: string (opcional)
        - referralCount: number
        - referredBy: string (opcional)
        - totalReward: number

    - withdrawals/
      - {withdrawalId}/
        - userId: string
        - userEmail: string
        - userCountry: string
        - userPhone: string (opcional)
        - amount: number
        - requestDate: timestamp
        - status: string (pending/approved/rejected)
        - type: string (mobile_recharge/gift_card)
        - details: object
        - processedDate: timestamp (opcional)
        - processedBy: string (opcional)
        - rejectionReason: string (opcional)

    - adAnalytics/
      - impressions/
        - records/
          - {impressionId}/
            - userId: string
            - timestamp: timestamp
            - adNetwork: string (AdMob/Unity/Meta)
            - adType: string (reward)
            - completed: boolean
            - country: string
            - appVersion: string
            - rewardAmount: number (opcional)
            - revenue: number (opcional, calculado desde eCPM)

      - config/
        - eCPM/
          - {network}-{adType}/
            - adNetwork: string
            - adType: string
            - countryRates: {
                US: number,
                MX: number,
                ...
              }
            - defaultRate: number
            - lastUpdate: timestamp

    - adminSettings/
      - withdrawalSettings/
        - minimumAmount: number
        - withdrawalPercentage: number
        - processingTimeHours: number
        - availableTypes: [string]
        - typeOptions: {
            mobile_recharge: [string],
            gift_card: [string]
          }

    - adminLogs/
      - {logId}/
        - adminId: string
        - adminEmail: string
        - action: string
        - timestamp: timestamp
        - details: object
```

## Flujo de Datos entre el Juego y el Sistema Web

### 1. Desde el Juego

**Registro/Login de Usuario**:
- **Colección**: `projects/pinataparty/users/{userId}`
- **Campos**: email, country, createAt, lastLoginAt
- **Cuándo**: Al registrarse o iniciar sesión en el juego

**Registro de Impresión de Anuncio**:
- **Colección**: `projects/pinataparty/adAnalytics/impressions/records/{impressionId}`
- **Campos**: userId, timestamp, adNetwork, adType, completed, country, appVersion
- **Cuándo**: Cada vez que un usuario completa la visualización de un anuncio

**Actualización de Contador Diario**:
- **Colección**: `projects/pinataparty/users/{userId}/adHistory/{year}/{month}/{day}`
- **Campos**: completedRewardAds, timestamp
- **Cuándo**: Cada vez que un usuario completa la visualización de un anuncio

### 2. Desde el Sistema Web

**Gestión de Usuarios**:
- **Colección**: `projects/pinataparty/users/{userId}`
- **Campos**: phoneNumber, isEmailVerified, isPhoneVerified, isActive, adEarnings, availableAdEarnings, completedRewardAds, processedRewardAds, lastUpdateAt, lastEarningsCalculation, lastWithdrawalDate, lastWithdrawalAmount, lastApprovedWithdrawalDate, lastApprovedWithdrawalAmount, totalApprovedWithdrawals, totalWithdrawnAmount, lastRejectedWithdrawalDate, lastRejectedWithdrawalAmount, lastRejectedWithdrawalReason
- **Cuándo**: Al actualizar datos de usuario, verificar contacto o calcular ganancias

**Gestión de Retiros**:
- **Colección**: `projects/pinataparty/withdrawals/{withdrawalId}`
- **Campos**: userId, userEmail, userCountry, userPhone, amount, requestDate, status, type, details
- **Cuándo**: Al solicitar, aprobar o rechazar un retiro

**Configuración del Sistema**:
- **Colección**: `projects/pinataparty/adminSettings/withdrawalSettings`
- **Campos**: minimumAmount, withdrawalPercentage, processingTimeHours, availableTypes, typeOptions
- **Cuándo**: Al actualizar la configuración del sistema

**Logs Administrativos**:
- **Colección**: `projects/pinataparty/adminLogs/{logId}`
- **Campos**: adminId, adminEmail, action, timestamp, details
- **Cuándo**: Cada vez que un administrador realiza una acción importante

## Cálculo de Ganancias

La fórmula para calcular las ganancias es:
```
Revenue = (Impressions × eCPM) / 1000
```
Donde:
- Impressions: Número de impresiones de anuncios
- eCPM: Effective Cost Per Mille (costo por mil impresiones)

#### Sistema Optimizado de Cálculo de Ganancias

Para evitar recalcular repetidamente las ganancias cuando los usuarios realizan retiros, el sistema implementa un enfoque incremental:

1. Se mantiene un contador `completedRewardAds` que registra el total de anuncios de recompensa completados
2. Se agrega un contador `processedRewardAds` que registra cuántos anuncios ya han sido procesados para cálculo de ganancias
3. Al calcular ganancias, solo se procesan los anuncios nuevos: `pendingAds = completedRewardAds - processedRewardAds`
4. Después de calcular las ganancias para los anuncios pendientes, se actualiza `processedRewardAds = completedRewardAds`
5. Este enfoque garantiza que cada anuncio se procese una sola vez para el cálculo de ganancias

### Sistema de Distribución de Ganancias Controlado por Administrador

El sistema implementa un enfoque de distribución de ganancias controlado por el administrador, que permite calcular y distribuir las ganancias de forma periódica (semanal, quincenal o mensual) con valores precisos de eCPM:

#### Conceptos Clave

1. **Ganancias Estimadas**: Un campo que muestra a los usuarios una aproximación de lo que han ganado desde la última distribución (solo informativo).
2. **Ganancias Confirmadas**: Las ganancias reales calculadas por el administrador periódicamente, que se suman al total acumulado del usuario.
3. **Proceso de Distribución**: Un proceso administrativo para calcular y distribuir ganancias en un rango de fechas específico.

#### Flujo de Trabajo

1. **Registro de Impresiones de Anuncios**:
   - El juego registra cada impresión de anuncio completada en `projects/pinataparty/adAnalytics/impressions/records`
   - Se actualiza el contador diario en `projects/pinataparty/users/$userId/adHistory/$year/$month/$day`
   - Se incrementa el contador total `completedRewardAds` en el perfil del usuario

2. **Cálculo de Ganancias Estimadas**:
   - Cuando el usuario inicia sesión o refresca la página, se calculan sus ganancias estimadas
   - Se utilizan valores conservadores de eCPM para cada red publicitaria y país
   - Estas ganancias estimadas se muestran como "pendientes de confirmar" en la interfaz

3. **Distribución de Ganancias por el Administrador**:
   - El administrador selecciona un rango de fechas para procesar (ej. del 1 al 15 de mayo)
   - Configura los valores reales de eCPM para cada red publicitaria y país, basados en los informes de las redes
   - Inicia el proceso de distribución, que calcula las ganancias para todos los usuarios activos
   - El sistema procesa todas las impresiones de anuncios en el rango de fechas seleccionado
   - Las ganancias calculadas se suman a los totales de cada usuario
   - Las ganancias estimadas se resetean a cero

4. **Registro de Distribuciones**:
   - Cada distribución se registra con detalles como fechas, valores de eCPM, total distribuido, etc.
   - Se mantiene un historial completo de todas las distribuciones realizadas

#### Ventajas del Sistema

- **Precisión**: Utiliza los valores reales de eCPM para cada período, basados en los informes de las redes publicitarias
- **Control**: El administrador decide cuándo y cómo se calculan y distribuyen las ganancias
- **Transparencia**: Los usuarios ven tanto sus ganancias confirmadas como una estimación de las pendientes
- **Flexibilidad**: Permite ajustar los valores de eCPM según sea necesario para cada distribución
- **Trazabilidad**: Mantiene un historial completo de todas las distribuciones realizadas

#### Estructura de Datos

**Perfil de Usuario Extendido**:
```
UserProfile {
  // Campos existentes
  adEarnings: double            // Ganancias confirmadas (total)
  availableAdEarnings: double   // Ganancias confirmadas disponibles para retiro

  // Nuevos campos
  estimatedEarnings: double     // Ganancias estimadas (no confirmadas)
  lastDistributionDate: DateTime // Fecha de la última distribución de ganancias
}
```

**Distribución de Ganancias**:
```
EarningsDistribution {
  id: string
  startDate: DateTime
  endDate: DateTime
  distributionDate: DateTime
  adminId: string
  adminEmail: string
  ecpmRates: Map<string, Map<string, double>> // Tasas de eCPM por red y país
  totalProcessedAds: int
  totalDistributedEarnings: double
  processedUserIds: List<string>
  status: string // 'pending', 'processing', 'completed', 'failed'
}
```

#### Cálculo de eCPM Multi-Red

El sistema calcula las ganancias considerando las tres redes publicitarias principales:

1. Para cada anuncio pendiente, se obtienen las configuraciones de eCPM de AdMob, Unity y Meta
2. Se calcula el promedio de las tarifas de eCPM para el país del usuario entre las tres redes
3. Si no se encuentra la configuración para alguna red, se utiliza un valor predeterminado
4. El sistema es resistente a fallos: si no se encuentra ninguna configuración, utiliza valores predeterminados
5. Este enfoque proporciona un cálculo más preciso y equilibrado de las ganancias

Este sistema optimizado se implementa en el método `UserProfile.calculateAndUpdateEarnings()` y se ejecuta automáticamente cuando:
- El usuario inicia sesión o recarga la página
- El usuario intenta realizar un retiro

#### Seguimiento de Retiros

El sistema ahora mantiene un registro detallado de los retiros de cada usuario:

1. **Retiros Solicitados**:
   - `lastWithdrawalDate`: Fecha del último retiro solicitado
   - `lastWithdrawalAmount`: Monto del último retiro solicitado

2. **Retiros Aprobados**:
   - `lastApprovedWithdrawalDate`: Fecha del último retiro aprobado
   - `lastApprovedWithdrawalAmount`: Monto del último retiro aprobado
   - `totalApprovedWithdrawals`: Contador total de retiros aprobados
   - `totalWithdrawnAmount`: Suma total de montos retirados

3. **Retiros Rechazados**:
   - `lastRejectedWithdrawalDate`: Fecha del último retiro rechazado
   - `lastRejectedWithdrawalAmount`: Monto del último retiro rechazado
   - `lastRejectedWithdrawalReason`: Razón del rechazo

Esta información permite un mejor seguimiento de la actividad de retiros y facilita la auditoría del sistema.

### Multiplicadores por País (countryMultipliers)

El sistema utiliza multiplicadores por país para ajustar las tarifas de eCPM según el país del usuario:

1. Cada país tiene un multiplicador específico para cada red publicitaria
2. El eCPM ajustado se calcula: `eCPM ajustado = eCPM base × multiplicador del país`
3. Esto permite reflejar las diferencias reales en el valor de las impresiones en diferentes mercados

Ejemplo:
- eCPM base para AdMob: $10.0
- Multiplicador para US: 1.2
- eCPM ajustado para US: $12.0
- Ingreso por impresión: $12.0 / 1000 = $0.012

## Sistema de Recompensas

Los usuarios pueden retirar hasta el 50% de sus ganancias generadas cuando alcancen un mínimo de $10 USD.

**Requisitos para solicitar retiros**:
- Tener correo electrónico verificado
- Alcanzar el mínimo de $10 USD disponibles para retirar

Opciones de retiro:
- Recarga móvil (usando el número de teléfono proporcionado)
- Tarjeta de regalo (usando el correo electrónico verificado)

El proceso de retiro es manual y debe ser aprobado por un administrador.

## Roles y Permisos

- **Administrador**:
  - Acceso total al dashboard
  - Gestión de configuración eCPM
  - Aprobación de retiros
  - Vista de estadísticas globales

- **Usuario**:
  - Vista de sus propias estadísticas
  - Solicitud de retiro
  - Historial de anuncios vistos
  - Actualización de perfil (teléfono)
  - Visualización de sistema de referidos

## Reglas de Seguridad de Firestore

Las reglas de seguridad de Firestore están diseñadas para:

1. Permitir acceso completo a todas las colecciones por defecto (para no interferir con otros proyectos)
2. Aplicar restricciones específicas solo a las colecciones dentro de `projects/pinataparty/`
3. Definir funciones helper (`isAdmin()`, `isAuthenticated()`, `isOwner()`) localmente dentro de cada regla

Ejemplo de estructura de reglas:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Regla general permisiva para no afectar otros proyectos
    match /{document=**} {
      allow read, write: if true;
    }

    // Reglas específicas para PP Analytics Reward
    match /projects/pinataparty/users/{userId} {
      // Funciones helper locales
      function isAdmin() {
        return request.auth != null &&
               request.auth.token.email != null &&
               request.auth.token.email.matches(".*@pinataparty\\.com");
      }

      function isAuthenticated() {
        return request.auth != null;
      }

      function isOwner(userId) {
        return request.auth != null && request.auth.uid == userId;
      }

      // Reglas de acceso
      allow read: if isAuthenticated() && (isAdmin() || isOwner(userId));
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isAdmin() || isOwner(userId));
      allow delete: if isAdmin();
    }
  }
}
```

## Project Overview

This project implements an analytics system to track and analyze ad performance data from the Piñata Party game using Flutter for the web administrative interface.

### Main Components

1. **Game App (Piñata Party)**: Minimal implementation to record reward ad impressions
2. **Web Admin System**: Flutter web application for data visualization and analysis

## Features

- 📊 Track ad views and rewards per user
- 💰 Calculate potential earnings based on eCPM
- 🌍 Geographic information analysis
- 📈 Period-based data analysis (daily, weekly, monthly)
- 📱 Lightweight game app integration

## Estructura de Implementación Actual

La implementación actual sigue esta estructura:

```
lib/
├── main.dart
├── firebase_options.dart
├── models/
│   ├── models.dart                  # Archivo de exportación para todos los modelos
│   ├── ad_impression.dart           # Modelo para impresiones de anuncios
│   ├── analytics_data.dart          # Modelo para datos de análisis
│   ├── ecpm_config.dart             # Configuración de eCPM por red y país
│   ├── user_data.dart               # Perfil de usuario simplificado
│   ├── user_management.dart         # Gestión completa de usuarios
│   ├── user_analytics_data.dart     # Datos de análisis por usuario
│   ├── withdrawal_management.dart   # Gestión de retiros
│   └── global_config.dart           # Configuración global del sistema
├── screens/
│   ├── login_screen.dart
│   ├── home_screen.dart
│   ├── user/
│   │   ├── user_dashboard_screen.dart    # Dashboard simplificado para usuarios
│   │   ├── user_profile_screen.dart     # Pantalla de perfil de usuario
│   │   ├── user_withdrawals_screen.dart # Pantalla de solicitud de retiros
│   │   └── user_history_screen.dart     # Pantalla de historial de retiros
│   └── admin/
│       ├── admin_dashboard_screen.dart   # Dashboard para administradores
│       ├── user_management_screen.dart  # Gestión de usuarios
│       ├── withdrawal_management_screen.dart # Gestión de retiros
│       └── user_detail_screen.dart      # Detalle de usuario
├── services/
│   ├── firebase_service.dart        # Servicios básicos de Firebase
│   ├── analytics_service.dart       # Servicios de análisis
│   ├── admin_service.dart           # Servicios administrativos
│   ├── user_service.dart            # Servicios específicos para usuarios
│   ├── cache_service.dart           # Servicios de caché local
│   └── test_data_service.dart       # Generación centralizada de datos de prueba
├── providers/
│   ├── analytics_provider.dart       # Proveedores para datos de análisis
│   ├── user_provider.dart           # Proveedores para datos de usuario
│   └── admin_provider.dart          # Proveedores para funciones administrativas
└── widgets/
    ├── charts/
    │   ├── impressions_chart.dart
    │   └── revenue_chart.dart
    └── common/
        ├── stats_card.dart
        ├── loading_indicator.dart
        ├── error_display.dart
        └── index_error_display.dart
```

## Getting Started

### Prerequisites

- Flutter SDK (latest version)
- Firebase project setup
- Web browser for development

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/ppanaliticsreward.git
cd ppanaliticsreward
```

2. Install dependencies:
```bash
flutter pub get
```

3. Configure Firebase:
- Create a new Firebase project
- Add web app to Firebase project
- Download and configure firebase_options.dart
- Enable Authentication and Firestore

4. Run the development server:
```bash
flutter run -d chrome --web-renderer html
```

## Development Commands

```bash
# Run in development mode
flutter run -d chrome --web-renderer html

# Build for production
flutter build web --release

# Deploy to Firebase Hosting (full deployment)
firebase deploy

# Deploy only hosting updates
firebase deploy --only hosting:ppanaliticreward

# Deploy only Firestore rules
firebase deploy --only firestore:rules

# Deploy only Firestore indexes
firebase deploy --only firestore:indexes
```

## Deployment Information

La aplicación está desplegada en Firebase Hosting con la siguiente configuración:

- **URL de producción**: [https://ppanaliticreward.web.app](https://ppanaliticreward.web.app)
- **Proyecto de Firebase**: portafoliodev-bd7f3
- **Target de hosting**: ppanaliticreward

Para actualizar la aplicación desplegada:

1. Construir la versión de producción: `flutter build web --release`
2. Desplegar solo los cambios de hosting: `firebase deploy --only hosting:ppanaliticreward`

Esto actualizará la aplicación web sin afectar las reglas de Firestore o los índices.

## Authentication

The system currently supports two types of users:
- Admin: <EMAIL> (with password)
- Regular user: <EMAIL> (with password)

### Verificación de Usuarios

El sistema implementa la verificación de correo electrónico para los usuarios:

1. **Verificación de Correo Electrónico**:
   - Se envía un enlace de verificación al correo del usuario
   - El usuario debe hacer clic en el enlace para verificar su correo
   - El estado de verificación se actualiza automáticamente en Firestore
   - Esta verificación es obligatoria para realizar retiros

### Usuario de Prueba para Verificación

- UUID: sQGXQH1D3yR3cTqr4s8KuOaWmRF3
- Correo: <EMAIL>
- País: US
- Teléfono: +13109337405

## Security and Privacy

- 🔒 Data anonymization
- 🛡️ Firestore security rules
- 🔐 Data encryption in transit and at rest
- 📜 GDPR/CCPA compliance

## Implementation Phases

1. **Phase 1**: Initial setup and authentication
2. **Phase 2**: Basic dashboard with simple charts
3. **Phase 3**: Detailed analytics implementation

## Completed Tasks
- ✅ Firebase integration
- ✅ User authentication
- ✅ Basic dashboard
- ✅ Data visualization
- ✅ Filtering by network and country
- ✅ Date range selection
- ✅ Restructured data models
- ✅ Implemented country multipliers
- ✅ Created test data generation
- ✅ Centralized test data generation
- ✅ Fixed dashboard inconsistency issues
- ✅ Implemented user management screen
- ✅ Implemented withdrawal management
- ✅ Simplified user interface
- ✅ Created user profile screen
- ✅ Deployed to Firebase Hosting
- ✅ Implemented dedicated screens for withdrawals and history
- ✅ Added referral system visualization
- ✅ Improved withdrawal request process with specific options
- ✅ Fixed Firestore security rules to avoid conflicts with other projects
- ✅ Implemented email verification for withdrawals
- ✅ Diseñado e implementado sistema de distribución de ganancias controlado por administrador
- ✅ Implementada visualización de ganancias estimadas en el dashboard del usuario
- ✅ Modificado el cálculo de ganancias para separar estimaciones de ganancias confirmadas
- ✅ Added visual verification indicators in user profile
- ✅ Improved navigation between user screens
- ✅ Implemented auto-fill of contact information in withdrawal requests
- ✅ Implementado verificación de correo electrónico con Firebase Authentication
- ✅ Simplificado proceso de retiros eliminando requisito de verificación de teléfono
- ✅ Mejorado manejo de número de teléfono como dato adicional sin verificación
- ✅ Actualizado despliegue a Firebase Hosting (Diciembre 2024)
- ✅ Implementado sistema optimizado de cálculo de ganancias (Enero 2025)
- ✅ Agregado campo processedRewardAds para evitar cálculos redundantes
- ✅ Mejorado sistema de seguimiento de retiros con campos adicionales
- ✅ Implementado registro detallado de retiros aprobados y rechazados
- ✅ Unificado el código de solicitud de retiros para evitar duplicación
- ✅ Mejorado cálculo de eCPM para considerar las tres redes publicitarias (AdMob, Unity, Meta)
- ✅ Rediseñado el diálogo de solicitud de retiro con un diseño centrado, scroll adaptativo y mejor organización visual
- ✅ Habilitada la edición del campo de contacto (teléfono/email) en el diálogo de solicitud de retiro
- ✅ Mejorada la interfaz de los botones en el diálogo de solicitud de retiro eliminando el contenedor innecesario
- ✅ Deshabilitada temporalmente la opción de "Tarjeta de Regalo" en el diálogo de solicitud de retiro, mostrando "(Próximamente)"
- ✅ Implementada la pantalla de configuración de eCPM para administradores
- ✅ Modificado el filtrado de rango de fechas en el dashboard del administrador para mostrar por defecto el día actual
- ✅ Corregido el problema con la carga del historial en la pantalla de Retiros pendientes
- ✅ Implementado sistema de caché para reducir consultas a Firestore y mejorar el rendimiento
- ✅ Optimizada la experiencia de usuario en la pantalla de Retiros pendientes con carga más rápida y fluida
- ✅ Mejorada la gestión de estado para optimizar el rendimiento general de la aplicación
- ✅ Corregido el problema con la fecha de registro que no se mostraba correctamente en la lista de usuarios
- ✅ Unificado el manejo de fechas entre UserManagement y UserProfile para asegurar consistencia
- ✅ Mejorado el formato de fecha para mostrar tanto la fecha como la hora
- ✅ Implementada búsqueda de campos de fecha tanto con createAt como con createdAt para mayor robustez
- ✅ Corregida la visualización de campos financieros para mostrar correctamente las ganancias disponibles
- ✅ Mejorado el formato de moneda para mostrar 4 decimales, permitiendo visualizar correctamente valores pequeños como 0.002

## Sistema de Distribución de Ganancias y Ganancias Estimadas

### Estado Actual de Implementación (Febrero 2025)

Se ha implementado un sistema completo de distribución de ganancias controlado por el administrador, con las siguientes características:

1. **Ganancias Estimadas para Usuarios**:
   - Los usuarios pueden ver una estimación de sus ganancias pendientes en el dashboard
   - Estas ganancias estimadas se calculan usando el mismo porcentaje de retiro que las ganancias reales
   - Se muestran en una tarjeta con fondo oscuro y texto ámbar para diferenciarlas de las ganancias confirmadas

2. **Panel de Distribución para Administradores**:
   - Los administradores pueden acceder a una nueva pantalla de distribución de ganancias
   - Pueden seleccionar un rango de fechas para procesar las ganancias
   - Pueden configurar los valores de eCPM por red publicitaria y país
   - El sistema procesa todas las impresiones de anuncios en el rango seleccionado

3. **Flujo de Trabajo de Ganancias**:
   - El juego registra las impresiones de anuncios completadas
   - El sistema calcula ganancias estimadas que se muestran al usuario
   - Periódicamente, el administrador realiza una distribución oficial
   - Las ganancias estimadas se resetean a cero después de cada distribución
   - Las ganancias confirmadas se suman al total acumulado del usuario

4. **Separación de Responsabilidades**:
   - El método `calculateAndUpdateEarnings` ahora solo actualiza las ganancias estimadas
   - El servicio `EarningsDistributionService` gestiona las distribuciones oficiales
   - Se mantiene un historial completo de todas las distribuciones realizadas

Este sistema proporciona mayor control, precisión y transparencia en el cálculo y distribución de ganancias, permitiendo usar los valores reales de eCPM para cada período.

## Lecciones Aprendidas
- Importancia de diseñar índices compuestos en Firestore desde el inicio para consultas complejas
- Necesidad de implementar un sistema de caché para reducir lecturas de Firestore
- Ventajas de usar Riverpod para gestión de estado y dependencias
- Importancia de la validación de datos en todas las capas de la aplicación
- Cuidado con los bucles infinitos en Riverpod cuando se usan proveedores con parámetros que cambian frecuentemente
- Preferir el filtrado en memoria para consultas complejas cuando el conjunto de datos es pequeño
- Usar StateNotifierProvider para manejar estados complejos con mejor control sobre cuándo se realizan las consultas

## Estrategias de Paginación en Firestore

A medida que la aplicación crece, es crucial implementar estrategias eficientes de paginación para manejar grandes volúmenes de datos. Firestore ofrece principalmente dos métodos de paginación:

### 1. Paginación basada en cursores

```dart
// Primera página
final firstPage = await firestore.collection('users')
    .orderBy('createAt', descending: true)
    .limit(10)
    .get();

// Siguiente página usando el último documento como cursor
final lastDocument = firstPage.docs.last;
final nextPage = await firestore.collection('users')
    .orderBy('createAt', descending: true)
    .startAfterDocument(lastDocument)
    .limit(10)
    .get();
```

**Ventajas**:
- Más robusta que la paginación basada en límites
- Maneja mejor los cambios en los datos
- Es la opción recomendada por Firebase

**Implementación**:
- Mantener una referencia al último documento de cada página
- Usar `startAfterDocument()` para obtener la siguiente página
- Requiere un ordenamiento consistente (por ejemplo, por `createAt`)

### 2. Estrategias para filtros complejos

Cuando se necesitan aplicar filtros complejos, se recomienda un enfoque híbrido:

```dart
// Aplicar filtros básicos en Firestore
Query query = firestore.collection('users');
if (isActive != null) {
  query = query.where('isActive', isEqualTo: isActive);
}

// Ordenar y paginar
query = query.orderBy('createAt', descending: true)
    .limit(adjustedLimit); // Obtener más documentos de los necesarios

// Aplicar filtros adicionales en memoria
List<UserManagement> filteredUsers = users.where((user) {
  // Filtros complejos que no se pueden aplicar directamente en Firestore
  return user.country.toUpperCase() == country.toUpperCase();
}).toList();
```

### 3. Consideraciones de rendimiento

- **Tamaño de página adaptativo**: Ajustar el tamaño de página según la complejidad de los filtros
- **Índices compuestos**: Crear índices para todas las combinaciones de filtros y ordenamientos
- **Caché local**: Almacenar resultados de consultas frecuentes para reducir lecturas
- **Prefetching**: Cargar la siguiente página antes de que el usuario la solicite
- **Monitoreo de cuotas**: Vigilar el uso de lecturas/escrituras para evitar costos excesivos

### 4. Estrategias de optimización de costos

A medida que la aplicación escala, es importante implementar estrategias para optimizar los costos de Firestore:

#### Reducción de lecturas
- **Caché local con persistencia**: Utilizar `shared_preferences` o `hive` para almacenar datos frecuentemente accedidos
- **Agrupación de consultas**: Combinar múltiples consultas pequeñas en una sola consulta más grande cuando sea posible
- **Consultas incrementales**: Cargar solo los datos nuevos o modificados desde la última consulta

#### Optimización de escrituras
- **Operaciones por lotes**: Utilizar `WriteBatch` para combinar múltiples operaciones de escritura
- **Actualización de campos específicos**: Usar `update()` con campos específicos en lugar de `set()` completo
- **Transacciones eficientes**: Minimizar el número de documentos leídos dentro de una transacción

#### Estructura de datos eficiente
- **Desnormalización estratégica**: Duplicar datos cuando sea necesario para reducir consultas
- **Documentos compuestos**: Almacenar datos relacionados en un solo documento cuando sea apropiado
- **Colecciones subcampo vs. subcolecciones**: Elegir la estructura adecuada según patrones de acceso

#### Monitoreo y ajuste
- **Análisis de patrones de uso**: Identificar las consultas más frecuentes y optimizarlas
- **Alertas de uso**: Configurar alertas para detectar picos inusuales en el uso
- **Pruebas de carga**: Simular cargas realistas para identificar cuellos de botella antes de que afecten a los usuarios reales

## Future Enhancements

### Estrategias de Paginación y Optimización de Datos
- **Implementar paginación basada en cursores**: Utilizar `startAfterDocument()` para consultas eficientes en colecciones grandes
- **Aplicar filtros en Firestore cuando sea posible**: Reducir la cantidad de datos transferidos aplicando filtros directamente en la consulta
- **Implementar sistema de caché local**: Almacenar datos frecuentemente accedidos para reducir consultas a Firestore
- **Estrategia de recarga inteligente**: Recargar datos solo cuando sea necesario, utilizando timestamps para detectar cambios
- **Optimización de consultas para colecciones grandes**:
  - Utilizar índices compuestos para consultas complejas
  - Ajustar el tamaño de página según la complejidad de los filtros
  - Implementar filtrado híbrido (Firestore + memoria) para casos especiales

### Mejoras Generales
- Implementar notificaciones para informar a los usuarios cuando se realiza una distribución
- Agregar más estadísticas en la pantalla de distribución
- Mejorar la interfaz de usuario para dispositivos móviles
- Implementar sistema de exportación de datos para análisis externos
- Añadir gráficos interactivos avanzados para visualización de tendencias
- Desarrollar una API para integración con herramientas de análisis externas
- Implementar un sistema de alertas para administradores sobre actividades inusuales

## Arquitectura de Autenticación

### Flujo de Autenticación

- **Juego (Pinata Party)**: Los usuarios pueden jugar de forma anónima, pero para guardar su progreso deben registrarse con email.
- **Sistema Web (PP Analytics)**: Los usuarios acceden exclusivamente mediante autenticación por email, no de forma anónima.
- **Número de Teléfono**: Se almacena como un dato adicional del usuario sin requerir verificación, no se utiliza como método de autenticación.

### Decisión de Arquitectura

Después de evaluar diferentes opciones, se ha decidido utilizar **exclusivamente autenticación por email** para el sistema web por las siguientes razones:

1. **Simplicidad**: Reduce la complejidad del sistema y facilita el mantenimiento
2. **Seguridad**: Evita posibles conflictos entre diferentes métodos de autenticación
3. **Experiencia de Usuario**: Proporciona un flujo de autenticación claro y consistente
4. **Compatibilidad**: Funciona perfectamente tanto en web como en dispositivos móviles

### Implementación Actual

- La autenticación por email está completamente implementada y funcionando correctamente
- El sistema envía enlaces de verificación al correo electrónico del usuario
- El estado de verificación se actualiza automáticamente en Firestore
- El número de teléfono se almacena como un dato adicional para fines de contacto y retiros
